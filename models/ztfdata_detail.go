package models

import (
	"strings"
	"time"

	"go-hetu/data"
	"go-hetu/helpers"
	utils2 "go-hetu/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
)

type ZtfDataDetail struct {
	Id             uint64 `gorm:"column:id"`               //id
	InstId         int    `gorm:"column:inst_id"`          //机构id
	InstName       string `gorm:"column:inst_name"`        //机构名称
	ChannelId      int    `gorm:"column:channel_id"`       //渠道id
	ChannelName    string `gorm:"column:channel_name"`     //渠道名称
	ChannelCode    string `gorm:"column:channel_code"`     //渠道编码
	ActId          int    `gorm:"column:act_id"`           //活动id
	Period         string `gorm:"column:period"`           //期次
	GradeDept      string `gorm:"column:grade_dept"`       //学部
	VisitNum       int    `gorm:"column:visit_num"`        //访问人数
	ClickNum       int    `gorm:"column:click_num"`        //点击人数
	GetQrNum       int    `gorm:"column:get_qr_num"`       //获取二维码人数
	ScanQrNum      int    `gorm:"column:scan_qr_num"`      //扫码人数
	LeadsNum       int    `gorm:"column:leads_num"`        //例子数
	CoefVisitNum   int    `gorm:"column:coef_visit_num"`   //访问人数（系数计算）
	CoefClickNum   int    `gorm:"column:coef_click_num"`   //点击人数（系数计算）
	CoefGetQrNum   int    `gorm:"column:coef_get_qr_num"`  //获取二维码人数（系数计算）
	CoefScanQrNum  int    `gorm:"column:coef_scan_qr_num"` //扫码人数（系数计算）
	CoefLeadsNum   int    `gorm:"column:coef_leads_num"`   //例子数（系数计算）
	TeamName       string `gorm:"column:team_name"`        //招生团队
	FlowTypeId     int64  `gorm:"column:flow_type_id"`     //流量类型-学部
	Cadre          string `gorm:"column:cadre"`            //归属人
	AppId          int64  `gorm:"column:app_id"`           //应用方id
	Dt             string `gorm:"column:dt"`               //日期分区
	LastModifyTime int64  `gorm:"column:last_modify_time"` //最后时间
}

func (ZtfDataDetail) TableName() string {
	return "tblZtfDataDetail"
}

func PageListZtfPeriod(ctx *gin.Context, query data.ZtfPeriodQuery) (total int64, list []data.ZftPeriodVo, err error) {
	_db := helpers.MysqlClient.WithContext(ctx).Model(&ZtfDataDetail{}).Distinct("period")

	if query.Period != "" {
		_db.Where("period like ?", "%"+query.Period+"%")
	}

	if err := _db.Count(&total).Error; err != nil {
		return total, list, err
	}

	_db.Scopes(NormalPaginate(&NormalPage{No: query.Pn, Size: query.Ps})).Find(&list)
	return total, list, nil
}

func ClearDataDetail(ctx *gin.Context) (err error) {
	dt := time.Now().AddDate(0, 0, -60).Format("20060102")
	err = helpers.MysqlClient.WithContext(ctx).Model(&ZtfDataDetail{}).Where("dt < ?", dt).Delete(&ZtfDataDetail{}).Error
	return
}

func PageListZtfDataDetail(ctx *gin.Context, query data.ZtfDataDetailQuery) (total int64, list []data.ZtfDataDetailVo, err error) {
	fields := []string{
		"sum(coef_visit_num) AS visit_uv",
		"sum(coef_click_num) AS click_uv",
		"sum(coef_get_qr_num) AS get_qr_uv",
		"sum(coef_scan_qr_num) AS scan_qr_uv",
		"sum(coef_leads_num) AS leads",
		"inst_id as inst_id",
		"inst_name as inst_name",
		"grade_dept as grade_dept",
	}

	var groupBy = []string{"inst_id"}
	dims := strings.Split(query.Dimension, ",")
	if utils.Contains(dims, "1") {
		fields = append(fields, "channel_name as channel")
		groupBy = append(groupBy, "channel_name")
	}
	if utils.Contains(dims, "2") {
		fields = append(fields, "period as period")
		groupBy = append(groupBy, "period")
	}
	if utils.Contains(dims, "3") {
		groupBy = append(groupBy, "grade_dept")
	}
	if utils.Contains(dims, "4") {
		fields = append(fields, "dt as dt")
		groupBy = append(groupBy, "dt")
	}
	if utils.Contains(dims, "5") {
		fields = append(fields, "act_id as act_id")
		groupBy = append(groupBy, "act_id")
	}
	if utils.Contains(dims, "6") {
		fields = append(fields, "team_name")
		groupBy = append(groupBy, "team_name")
	}
	if utils.Contains(dims, "7") {
		fields = append(fields, "cadre")
		groupBy = append(groupBy, "cadre")
	}
	if utils.Contains(dims, "8") {
		fields = append(fields, "flow_type_id")
		groupBy = append(groupBy, "flow_type_id")
	}
	_db := helpers.MysqlClient.WithContext(ctx).Model(&ZtfDataDetail{}).Select(fields)
	if query.InstIds != "" {
		_db.Where("inst_id in ?", utils2.SplitToIntSlice(query.InstIds, ","))
	}
	if query.GradeDepts != "" {
		_db.Where("grade_dept in ?", strings.Split(query.GradeDepts, ","))
	}
	if query.Periods != "" {
		_db.Where("period in ?", strings.Split(query.Periods, ","))
	}
	if query.StartDate != "" && query.EndDate != "" {
		_db.Where("dt >= ? and dt <= ?", query.StartDate, query.EndDate)
	}
	if query.ActId > 0 {
		_db.Where("act_id =?", query.ActId)
	}
	if len(query.AppId) > 0 {
		_db.Where("app_id in ?", utils2.SplitToIntSlice(query.AppId, ","))
	}
	if query.TeamName != "" {
		_db.Where("team_name in ?", strings.Split(query.TeamName, ","))
	}
	if query.FlowType != "" {
		_db.Where("flow_type_id in ?", strings.Split(query.FlowType, ","))
	}
	if len(query.AuthFlowTypeIds) > 0 {
		_db.Where("flow_type_id in ?", query.AuthFlowTypeIds)
	}
	if len(query.Cadre) != 0 {
		_db.Where("cadre in ?", query.Cadre)
	}
	if len(query.AuthUsers) != 0 {
		_db.Where("cadre in ?", query.AuthUsers)
	}
	_db.Group(strings.Join(groupBy, ","))
	_db.Order("inst_id, period desc ,id asc ")

	if !query.Export {
		if err := _db.Count(&total).Error; err != nil {
			return total, list, err
		}
		_db.Scopes(NormalPaginate(&NormalPage{No: query.Pn, Size: query.Ps}))
	}
	err = _db.Find(&list).Error
	return total, list, err
}
