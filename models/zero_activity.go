package models

type ZeroActivity struct {
	ID              int    `gorm:"column:id;primaryKey"`                        // 外呼任务ID
	ActyName        string `gorm:"column:acty_name"`                            // 活动名字
	TaskID          int    `gorm:"column:task_id"`                              // 群控h5任务ID
	TaskName        string `gorm:"column:task_name"`                            // 群控h5任务名称
	URL             string `gorm:"column:url"`                                  // 魔方0转正活动链接
	IsFlow          int    `gorm:"column:is_flow"`                              // 是否分流 0不分流, 1分流
	FlowBusiness    int    `gorm:"column:flow_business"`                        // 是否分流 分流业务线 1:K9商务一组 2:K10
	STaskID         int    `gorm:"column:s_task_id"`                            // 群控h5任务ID
	STaskName       string `gorm:"column:s_task_name"`                          // 群控h5任务名称
	ATaskID         int    `gorm:"column:a_task_id"`                            // 群控h5任务ID
	ATaskName       string `gorm:"column:a_task_name"`                          // 群控h5任务名称
	B1TaskID        int    `gorm:"column:b1_task_id"`                           // 群控h5任务ID
	B1TaskName      string `gorm:"column:b1_task_name"`                         // 群控h5任务名称
	B2TaskID        int    `gorm:"column:b2_task_id"`                           // 群控h5任务ID
	B2TaskName      string `gorm:"column:b2_task_name"`                         // 群控h5任务名称
	CTaskID         int    `gorm:"column:c_task_id"`                            // 群控h5任务ID
	CTaskName       string `gorm:"column:c_task_name"`                          // 群控h5任务名称
	UnknownTaskID   int    `gorm:"column:unknown_task_id"`                      // 群控h5任务ID
	UnknownTaskName string `gorm:"column:unknown_task_name"`                    // 群控h5任务名称
	Deleted         int    `gorm:"column:deleted"`                              // 0未删除, 1删除
	OperatorID      ID     `gorm:"column:operator_id"`                          // 操作人ID
	OperatorName    string `gorm:"column:operator_name"`                        // 操作人姓名
	CreateTime      int    `gorm:"column:create_time;autoCreateTime;<-:create"` // 创建时间
	UpdateTime      int    `gorm:"column:update_time;autoUpdateTime"`           // 更新时间
}

func (m *ZeroActivity) TableName() string {
	return "tblZeroActivity"
}
