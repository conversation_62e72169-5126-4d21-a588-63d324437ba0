package mq

import (
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"go-hetu/data"
	"go-hetu/helpers"
	"go-hetu/middleware"
	"testing"
)

var (
	ctx *gin.Context
)

func TestMain(m *testing.M) {
	// 设置项目根路径
	env.SetRootPath("../..")

	// 初始化基础配置
	helpers.PreInit()

	// 初始化数据库
	helpers.InitMysql()

	// 创建测试上下文
	ctx = &gin.Context{}

	// 设置测试用户信息（用于批量操作测试）
	ctx.Set("IPS_UserInfo", &middleware.UserInfo{
		Uname: "test_user",
		Uid:   12345,
	})
}

func TestTag900001Consumer(t *testing.T) {
	err := DoTag900001(ctx, data.AddInstShutConfig{
		InstIDs:      []int64{11487},
		OperatorType: 1,
	})
	if err != nil {
		return
	}
}
