package mq

import (
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func RmqConsumerInstActShut(ctx *gin.Context, msg rmq.Message) (err error) {
	defer func() {
		if err := recover(); err != nil {
			zlog.Warnf(ctx, "RMQConsumerPanic[recover]", zap.Any("error", err))
		}
	}()

	zlog.Info(ctx, "got message id=", msg.GetID(), " shard=", msg.GetShard(), " tag=", msg.GetTag(), " content=", string(msg.GetContent()))

	switch msg.GetTag() {
	case "900001": // 添加机构关停配置成功
		err = Tag900001Consumer(ctx, msg)
	case "900002":
		err = Tag900002Consumer(ctx, msg)
	default:
		zlog.Infof(ctx, "ignore tagID: %v", msg.GetTag())
		return nil
	}
	return err
}
