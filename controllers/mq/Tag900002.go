package mq

import (
	"encoding/json"
	"errors"
	"fmt"
	"go-hetu/data"
	"go-hetu/utils"
	"strconv"
	"strings"
	"time"

	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"gorm.io/gorm"
)

func Tag900002Consumer(ctx *gin.Context, rmqMsg rmq.Message) (err error) {
	startTime := time.Now()
	var msg = data.AddActShutConfig{}
	if err = json.Unmarshal(rmqMsg.GetContent(), &msg); err != nil {
		zlog.Warnf(ctx, "json unmarshal error: %s", err)
		return err
	}
	zlog.Infof(ctx, "Tag900002Consumer: %+v", msg)

	if err = processActInstShut(ctx, msg.ActID); err != nil {
		zlog.Errorf(ctx, "processActInstShut failed for actId=%d, err=%v", msg.ActID, err)
		return err
	}

	// 记录处理结果和性能指标
	duration := time.Since(startTime)
	zlog.Infof(ctx, "Tag900002Consumer completed: actId=%d, duration=%v", msg.ActID, duration)

	return nil
}

// processActInstShut 处理单个活动的机构关停配置
func processActInstShut(ctx *gin.Context, actId int64) (err error) {
	zlog.Infof(ctx, "Processing act inst shut for actId: %d", actId)

	var actInfo = tfmodels.TblAFXAct{}
	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAfxAct).
		Where("id = ? AND deleted = 0", actId).
		First(&actInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			zlog.Warnf(ctx, "Activity not found for actId=%d", actId)
			return nil
		}
		return err
	}
	// 如果活动截止时间小于当前时间，说明活动已经结束，无需处理
	if int64(actInfo.EndTime) < time.Now().Unix() {
		return nil
	}
	// 根据活动查询所属的所有机构
	instIds, err := getInstsByActivity(ctx, actInfo)
	if err != nil {
		zlog.Errorf(ctx, "getInstsByActivity failed for actId=%d, err=%v", actId, err)
		return err
	}

	if len(instIds) == 0 {
		zlog.Infof(ctx, "No institutions found for actId=%d", actId)
		return nil
	}

	zlog.Infof(ctx, "Found %d institutions for actId=%d", len(instIds), actId)

	// 根据instId查询表tblAFXInstToggleConfig是否有配置
	validInstConfigs, err := filterValidInsts(ctx, instIds)
	if err != nil {
		zlog.Errorf(ctx, "filterValidInsts failed for actId=%d, err=%v", actId, err)
		return err
	}

	if len(validInstConfigs) == 0 {
		zlog.Infof(ctx, "No valid institutions found for actId=%d", actId)
		return nil
	}

	zlog.Infof(ctx, "Found %d valid institutions for actId=%d", len(validInstConfigs), actId)

	// 向表tblAFXInstActToggleConfig中新增记录
	if err = insertActInstToggleConfig(ctx, validInstConfigs, actId); err != nil {
		zlog.Errorf(ctx, "insertActInstToggleConfig failed for actId=%d, err=%v", actId, err)
		return err
	}

	zlog.Infof(ctx, "Successfully processed %d institutions for actId=%d", len(validInstConfigs), actId)
	return nil
}

// getInstsByActivity 根据活动查询所属的所有机构
func getInstsByActivity(ctx *gin.Context, actInfo tfmodels.TblAFXAct) (instIds []int64, err error) {
	// 根据rule_type处理不同的逻辑
	switch actInfo.RuleType {
	case 1: // 白名单机构
		return getWhiteListInsts(ctx, actInfo.ExtData)
	case 0: // 所有机构
		return getAllInsts(ctx, actInfo.PromoteBusiness, utils.SplitToIntSlice(actInfo.PromoteGradeDepts, ","), actInfo.PromoteLv1New, actInfo.ChannelLabels)
	default:
		zlog.Warnf(ctx, "Unknown rule_type=%d for actId=%d", actInfo.RuleType, actInfo.ID)
		return []int64{}, nil
	}
}

// getWhiteListInsts 获取白名单机构
func getWhiteListInsts(ctx *gin.Context, extData string) (instIds []int64, err error) {
	if extData == "" {
		return []int64{}, nil
	}

	// 解析JSON格式的ext_data
	var extDataMap map[string]interface{}
	if err := json.Unmarshal([]byte(extData), &extDataMap); err != nil {
		zlog.Warnf(ctx, "Failed to parse ext_data: %v", err)
		return []int64{}, nil
	}

	// 获取whiteList
	whiteListInterface, exists := extDataMap["whiteList"]
	if !exists {
		return []int64{}, nil
	}

	// 转换为字符串数组
	whiteListArray, ok := whiteListInterface.([]interface{})
	if !ok {
		return []int64{}, nil
	}

	// 转换为int64数组
	for _, item := range whiteListArray {
		if itemStr, ok := item.(string); ok {
			if instId, err := strconv.ParseInt(itemStr, 10, 64); err == nil {
				instIds = append(instIds, instId)
			}
		}
	}

	return instIds, nil
}

// getAllInsts 获取所有符合条件的机构
func getAllInsts(ctx *gin.Context, promoteBusiness int, promoteGradeDepts []int, promoteLv1New int, channelLabels string) (instIds []int64, err error) {
	// 解析channel_labels获取tag_ids
	tagIds := parseChannelLabels(channelLabels)

	// 构建查询条件
	query := helpers.MysqlClientQD.WithContext(ctx).Table(tfmodels.TblQudaoInstRegister).
		Select("DISTINCT inst_id").
		Where("run_status in (1, 2) AND deleted = 0")

	// 添加业务线、学部、推广产品条件
	if promoteBusiness > 0 {
		query = query.Where("promote_business = ?", promoteBusiness)
	}
	if len(promoteGradeDepts) > 0 {
		query = query.Where("promote_grade_dept in ?", promoteGradeDepts)
	}
	if promoteLv1New > 0 {
		query = query.Where("promote_lv1_new = ?", promoteLv1New)
	}

	// 如果有渠道标签，需要检查tag_info
	if len(tagIds) > 0 {
		// 先查询所有符合条件的记录，然后在应用层过滤
		type RegisterRecord struct {
			InstID  int64  `gorm:"column:inst_id"`
			TagInfo string `gorm:"column:tag_info"`
		}

		var records []RegisterRecord
		if err = query.Select("DISTINCT inst_id, tag_info").Find(&records).Error; err != nil {
			return nil, fmt.Errorf("query register records failed: %w", err)
		}

		// 在应用层过滤有匹配标签的机构
		for _, record := range records {
			if hasMatchingTags(record.TagInfo, tagIds) {
				instIds = append(instIds, record.InstID)
			}
		}
	} else {
		// 没有渠道标签限制，直接查询
		if err = query.Pluck("inst_id", &instIds).Error; err != nil {
			return nil, fmt.Errorf("query institution ids failed: %w", err)
		}
	}

	return removeDuplicateInt64(instIds), nil
}

// parseChannelLabels 解析channel_labels字段
func parseChannelLabels(channelLabels string) []int {
	if channelLabels == "" {
		return []int{}
	}

	// channel_labels格式为 ",3,2,1,"
	// 去掉首尾的逗号，然后分割
	channelLabels = strings.Trim(channelLabels, ",")
	if channelLabels == "" {
		return []int{}
	}

	tagStrs := strings.Split(channelLabels, ",")
	var tagIds []int
	for _, tagStr := range tagStrs {
		if tagStr != "" {
			if tagId, err := strconv.Atoi(tagStr); err == nil {
				tagIds = append(tagIds, tagId)
			}
		}
	}

	return tagIds
}

// hasMatchingTags 检查tag_info是否与渠道标签匹配
func hasMatchingTags(tagInfo string, targetTagIds []int) bool {
	if tagInfo == "" || len(targetTagIds) == 0 {
		return false
	}

	// 解析tag_info: [{"key":1,"quantity":100}]
	var tagArray []map[string]interface{}
	if err := json.Unmarshal([]byte(tagInfo), &tagArray); err != nil {
		return false
	}

	// 提取tag_info中的key
	tagInfoIds := make(map[int]bool)
	for _, tag := range tagArray {
		if keyInterface, exists := tag["key"]; exists {
			if keyFloat, ok := keyInterface.(float64); ok {
				tagInfoIds[int(keyFloat)] = true
			}
		}
	}

	// 检查是否有交集
	for _, targetTagId := range targetTagIds {
		if tagInfoIds[targetTagId] {
			return true
		}
	}

	return false
}

// filterValidInsts 过滤有效的机构（在tblAFXInstToggleConfig中有配置的）
func filterValidInsts(ctx *gin.Context, instIds []int64) (validInstConfigs []tfmodels.AFXInstToggleConfig, err error) {
	validInstConfigs = make([]tfmodels.AFXInstToggleConfig, 0)
	if len(instIds) == 0 {
		return validInstConfigs, nil
	}

	// 查询在tblAFXInstToggleConfig中有配置的机构
	var configs []tfmodels.AFXInstToggleConfig
	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).
		Where("inst_id IN ? AND deleted = 0", instIds).
		Find(&configs).Error; err != nil {
		return nil, fmt.Errorf("query inst toggle config failed: %w", err)
	}

	return configs, nil
}

// insertActInstToggleConfig 向tblAFXInstActToggleConfig表中新增记录
func insertActInstToggleConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig, actId int64) (err error) {
	if len(instConfigs) == 0 {
		return nil
	}

	var (
		instIds       = make([]int64, 0)
		instConfigMap = make(map[int64]tfmodels.AFXInstToggleConfig)
	)
	for _, config := range instConfigs {
		instIds = append(instIds, config.InstID)
		instConfigMap[config.InstID] = config
	}

	// 使用事务确保数据一致性
	return helpers.ExecWithTransaction(ctx, helpers.MysqlClient, func(tx *gorm.DB) (err error) {
		// 检查已存在的记录
		var (
			existingInstActIds []int64
		)
		if err = tx.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
			Where("inst_id IN ? AND act_id = ? AND deleted = 0", instIds, actId).
			Pluck("inst_id", &existingInstActIds).Error; err != nil {
			return fmt.Errorf("check existing records failed: %w", err)
		}

		// 分类已存在和不存在的记录
		existingMap := make(map[int64]struct{})
		for _, instId := range existingInstActIds {
			existingMap[instId] = struct{}{}
		}

		var (
			instActConfigs []tfmodels.AFXInstActToggleConfig
		)
		for _, instId := range instIds {
			if _, ok := existingMap[instId]; !ok {
				instActConfigs = append(instActConfigs, tfmodels.AFXInstActToggleConfig{
					InstID:   instId,
					ActID:    actId,
					Status:   instConfigMap[instId].Status, // 使用operatorType作为状态值
					Operator: "system",
					Deleted:  0,
				})
			}
		}

		// 批量插入
		if err = tx.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
			CreateInBatches(instActConfigs, 50).Error; err != nil {
			return fmt.Errorf("batch insert inst act toggle config failed: %w", err)
		}
		return nil
	})
}
