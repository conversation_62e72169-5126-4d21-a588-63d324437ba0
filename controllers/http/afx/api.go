package afx

import (
	"fmt"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/spf13/cast"
	"go-hetu/middleware"
	"regexp"
	"strings"
	"time"

	"git.zuoyebang.cc/ad/gocommons/utils"

	"go-hetu/components"
	"go-hetu/data"
	"go-hetu/helpers"
	"go-hetu/services/afx"

	"git.zuoyebang.cc/ad/gocommons/const/fenxiaoconsts"
	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"

	"github.com/gin-gonic/gin"
)

type resource struct {
	service afx.Svcer
}

func RegisterHandlers(
	afxRouter *gin.RouterGroup,
) {
	afxService := afx.InitService(dbcontext.New(helpers.MysqlClient))

	r := resource{
		service: afxService,
	}

	afxRouter.POST("organlist", r.organList)          // 一级代理商管理-列表&搜索         https://yapi.zuoyebang.cc/project/9101/interface/api/779656
	afxRouter.GET("registerinst", r.getRegisterInst)  // 一级代理商管理-获取已报备代理机构  https://yapi.zuoyebang.cc/project/9101/interface/api/780034
	afxRouter.GET("fxregisterinst", r.fxRegisterInst) // 一级代理商管理-查询报备信息       https://yapi.zuoyebang.cc/project/9101/interface/api/780538
	afxRouter.GET("checkagent", r.checkAgent)         // 一级代理商管理-校验是否已添加     https://yapi.zuoyebang.cc/project/9101/interface/api/779800
	afxRouter.POST("organsave", r.organSave)          // 一级代理商管理-保存&编辑         https://yapi.zuoyebang.cc/project/9101/interface/api/780025

	afxRouter.GET("deuserinfo", r.deUserInfo)     // 通用-获取用户手机号     https://yapi.zuoyebang.cc/project/9101/interface/api/780052
	afxRouter.GET("selectlist", r.selectList)     // 通用-获取筛选项中内容    https://yapi.zuoyebang.cc/project/9101/interface/api/781447
	afxRouter.GET("saleTypeInfo", r.saleTypeInfo) // 通用-获取售卖相关筛选项  https://yapi.zuoyebang.cc/project/9101/interface/api/781438
	afxRouter.GET("cadre", r.getCadreFromALPS)    // 通用-获取在职员工信息

	afxRouter.POST("userlist", r.userList) // 代理商管理-列表 https://yapi.zuoyebang.cc/project/9101/interface/api/780043
	afxRouter.GET("getuserinfo", r.getUserInfo)
	afxRouter.GET("options", r.options)
	afxRouter.POST("mockreleaseinst", r.mockReleaseInst)
}

// 一级代理商列表
func (r resource) organList(ctx *gin.Context) {
	var params data.AfxOrganListReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	list, err := r.service.GetList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, list)
}

// 一级代理商列表
func (r resource) options(ctx *gin.Context) {
	list, err := r.service.Options(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)
}

func (r resource) getRegisterInst(ctx *gin.Context) {
	var params data.AfxRegisterInstReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	data, err := r.service.GetRegisterInst(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, data)
}

func (r resource) checkAgent(ctx *gin.Context) {
	var params data.AfxCheckAgentReq
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warnf(ctx, "[hetu]organSave params err=[%+v]", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	res, err := r.service.GetCheckAgentRes(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, res)
}

func (r resource) fxRegisterInst(ctx *gin.Context) {
	var params data.AfxFxRegisterInstReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	// 高中逻辑特殊处理
	if params.AppId == fenxiaoconsts.FenxiaoAuthAppMap[fenxiaoconsts.UserDataRoleGZSC] {
		params.IsGZ = 1
	}

	data, err := r.service.GetFxRegisterInst(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, data)
}

func (r resource) getUserInfo(ctx *gin.Context) {
	var params data.AfxUserInfoReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	data, err := r.service.GetUserInfo(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	base.RenderJsonSucc(ctx, data)
}

func (r resource) organSave(ctx *gin.Context) {
	var params data.AfxOrganSaveReq
	if err := ctx.ShouldBind(&params); err != nil {
		zlog.Warnf(ctx, "[hetu]organSave params err=[%+v]", err)
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	//if len(params.PromoteValue) == 0 {
	//	components.ErrorParamInvalid.ErrMsg = "请传入推广产品一级"
	//	base.RenderJsonFail(ctx, components.ErrorParamInvalid)
	//	return
	//}
	if len(params.PromoteList) == 0 {
		components.ErrorParamInvalid.ErrMsg = "请传入业务线|学部|新推广产品"
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	} else {
		params.ComplexPromote = map[string]int{}
		for _, info := range params.PromoteList {
			if info.PromoteBusiness <= 0 || info.PromoteGradeDept <= 0 || info.PromoteLv1New <= 0 {
				components.ErrorParamInvalid.ErrMsg = "业务线|学部|新推广产品 存在空值"
				base.RenderJsonFail(ctx, components.ErrorParamInvalid)
				return
			}
			params.ComplexPromote[afx.GetComplexPromote(info.PromoteLv1, info.PromoteBusiness, info.PromoteGradeDept, info.PromoteLv1New)] = 1
		}
	}

	if len(params.SaleTypeInfo) == 0 {
		components.ErrorParamInvalid.ErrMsg = "请传入售卖类型"
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	if !strings.Contains(params.Mobile, "*") {
		regular := `^1[0-9]{10}$`
		if !regexp.MustCompile(regular).MatchString(params.Mobile) {
			components.ErrorParamInvalid.ErrMsg = "手机号码不合规，请重新填写"
			base.RenderJsonFail(ctx, components.ErrorParamInvalid)
			return
		}
	} else {
		params.Mobile = ""
	}

	data, err := r.service.OrganSave(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, data)
}

func (r resource) deUserInfo(ctx *gin.Context) {
	var params data.AfxDeUserInfoReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	info, err := r.service.GetDeUserInfo(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, info)
}

func (r resource) selectList(ctx *gin.Context) {
	var params data.SelectListRequest
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	list, err := r.service.GetSelectList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, list)
}

func (r resource) userList(ctx *gin.Context) {
	var params data.AfxUserListReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}

	list, err := r.service.GetUserList(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, list)
}

func (r resource) saleTypeInfo(ctx *gin.Context) {
	list, err := r.service.GetSaleTypeInfo(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, list)
}

func (r resource) getCadreFromALPS(ctx *gin.Context) {
	var params data.AfxGetCadreReq
	if err := ctx.ShouldBind(&params); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	list, err := r.service.GetCadreFromALPS(ctx, params)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	authUsers := middleware.GetCtxUserAuthUser(ctx)
	res := make([]string, 0)
	for i := range list {
		if authUsers != nil && len(authUsers) > 0 && !utils.InArrayStr(list[i], authUsers) {
			continue
		}
		res = append(res, list[i])
	}
	base.RenderJsonSucc(ctx, res)
}

func InitInstDataFix(ctx *gin.Context) {
	param := struct {
		Limit int `json:"limit" form:"limit"`
	}{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
	startTime := 1702535420
	endTime := 1702535645
	err := svc.ScriptFixInstData(ctx, startTime, endTime, param.Limit)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func ScriptFixUserRelation(ctx *gin.Context) {
	param := struct {
		Limit int `json:"limit" form:"limit"`
	}{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
	err := svc.ScriptFixUserRelation(ctx, param.Limit)
	if err != nil {
		zlog.Warnf(ctx, "ScriptFixUserRelation err:%s", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func ScriptFixOrderCadre(ctx *gin.Context) {
	param := struct {
		MethodType string `json:"methodType" form:"methodType"`
		StartTime  int64  `json:"startTime" form:"startTime"`
		EndTime    int64  `json:"endTime" form:"endTime"`
		InstIDs    string `json:"instIDs" form:"instIDs"`
	}{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	var instIDs []int64
	for _, instIDStr := range strings.Split(param.InstIDs, ",") {
		instID := cast.ToInt64(instIDStr)
		if instID > 0 {
			instIDs = append(instIDs, instID)
		}
	}
	svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
	err := svc.ScriptFixOrderCadre(ctx, param.MethodType, instIDs, param.StartTime, param.EndTime)
	if err != nil {
		zlog.Warnf(ctx, "ScriptFixOrderCadre err:%s", err.Error())
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func FixAfxUserCadre(ctx *gin.Context) {
	ctx2 := ctx.Copy()
	go components.WithRecover(ctx2, func() {
		param := struct {
			MethodType string `json:"methodType" form:"methodType"`
		}{}
		if err := ctx.ShouldBind(&param); err != nil {
			base.RenderJsonFail(ctx, components.ErrorParamInvalid)
			return
		}
		svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
		err := svc.FixAfxUserCadre(ctx, param.MethodType)
		if err != nil {
			zlog.Warnf(ctx, "FixAfxUserCadre err:%s", err.Error())
			base.RenderJsonFail(ctx, err)
			return
		}
	})
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) mockReleaseInst(ctx *gin.Context) {
	if env.GetRunEnv() != env.RunEnvTest {
		if env.GetRunEnv() == env.RunEnvTips && time.Now().Unix() < time.Date(2025, 5, 10, 0, 0, 0, 0, time.Local).Unix() {
			// 上线测试mock数据，只允许在上线当天tips环境使用
		} else {
			base.RenderJsonFail(ctx, components.ErrorParamInvalid)
			return
		}

	}
	var msg data.RmqAfxReleaseInstNewReq
	if err := ctx.ShouldBind(&msg); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
	err := svc.RmqReleaseInstNew(ctx, msg)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
	return
}

func FixAFXUserRelationData(ctx *gin.Context) {
	param := struct {
		MethodType string `json:"methodType" form:"methodType"`
	}{}
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	svc := afx.InitService(dbcontext.New(helpers.MysqlClient))
	err := svc.FixAFXUserRelationData(ctx, param.MethodType)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func RefreshFLowTypeData(ctx *gin.Context) {
	tx := helpers.MysqlClient.WithContext(ctx).Begin()
	var err error
	defer func() {
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}()
	// promotionrelation category=3删除，新增category=5对应条目
	prs := make([]tfmodels.TblAFXPromoteRelation, 0)
	err = tx.Model(&tfmodels.TblAFXPromoteRelation{}).Where("category = 3 and deleted = 0").Find(&prs).Error
	if err != nil {
		return
	}
	addprs := make([]tfmodels.TblAFXPromoteRelation, 0)
	uniqueMap := make(map[string]bool)
	for _, pr := range prs {
		newValue := getNewRelationType(pr.CategoryValue)
		if newValue == -1 {
			err = fmt.Errorf("PromoteRelation id:%d, CategoryValue: %d err", pr.ID, pr.CategoryValue)
			base.RenderJsonFail(ctx, err)
			return
		}
		key := fmt.Sprintf("%d_%d", pr.PromoteLv1, newValue)
		if !uniqueMap[key] {
			addprs = append(addprs, tfmodels.TblAFXPromoteRelation{
				PromoteLv1:    pr.PromoteLv1,
				Category:      5,
				CategoryValue: newValue,
				CreateTime:    int(time.Now().Unix()),
				UpdateTime:    int(time.Now().Unix()),
			})
			uniqueMap[key] = true
		}
	}
	err = tx.Model(&tfmodels.TblAFXPromoteRelation{}).Where("category = 3 and deleted = 0").
		Update("deleted", 1).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	err = tx.Model(&tfmodels.TblAFXPromoteRelation{}).CreateInBatches(addprs, 10).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	// userrelation relationType=3保留，新增relationType=5条目
	urs := make([]tfmodels.TblAFXUserRelation, 0)
	err = tx.Model(&tfmodels.TblAFXUserRelation{}).Where("relation_type = 3 and status = 1").Find(&urs).Error
	if err != nil {
		return
	}
	addurs := make([]tfmodels.TblAFXUserRelation, 0)
	uniqueMap2 := make(map[string]bool)
	for _, ur := range urs {
		newValue := getNewRelationType(ur.RelationValue)
		if newValue == -1 {
			err = fmt.Errorf("UserRelation id:%d, RelationValue: %d err", ur.ID, ur.RelationValue)
			base.RenderJsonFail(ctx, err)
			return
		}
		key := fmt.Sprintf("%d_%d_%d_%d_%s", ur.PromoteLv1, newValue, ur.InstID, ur.AppID, ur.Cadre)
		if !uniqueMap2[key] {
			addurs = append(addurs, tfmodels.TblAFXUserRelation{
				InstID:        ur.InstID,
				AppID:         ur.AppID,
				Cadre:         ur.Cadre,
				Status:        ur.Status,
				StartTime:     ur.StartTime,
				EndTime:       ur.EndTime,
				PromoteLv1:    ur.PromoteLv1,
				RelationType:  5,
				RelationValue: newValue,
				CreateTime:    time.Now().Unix(),
				UpdateTime:    time.Now().Unix(),
			})
			uniqueMap2[key] = true
		}
	}
	err = tx.Model(&tfmodels.TblAFXUserRelation{}).CreateInBatches(addurs, 10).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	// datadetail新增teamname对应flowtypeId
	err = tx.Model(&tfmodels.ZtfDataDetail{}).Where("team_name=?", "端外-市场-小学").
		Update("flow_type_id", 11).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	err = tx.Model(&tfmodels.ZtfDataDetail{}).Where("team_name=?", "端外-市场-初中").
		Update("flow_type_id", 12).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	err = tx.Model(&tfmodels.ZtfDataDetail{}).Where("team_name=?", "端外-市场-高中").
		Update("flow_type_id", 13).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	err = tx.Model(&tfmodels.ZtfDataDetail{}).Where("team_name in ?", []string{"端外-群转-小学", "端外-进校-小学"}).
		Update("flow_type_id", 21).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	err = tx.Model(&tfmodels.ZtfDataDetail{}).Where("team_name=?", "端外-市场-初中").
		Update("flow_type_id", 22).Error
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
}

func getNewRelationType(old int) int {
	switch old {
	case 1:
		return 11
	case 2:
		return 12
	case 3:
		return 13
	case 4, 6:
		return 21
	case 5:
		return 22
	}
	return -1
}
