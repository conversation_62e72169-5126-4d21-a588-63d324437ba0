package ztf

import (
	"fmt"
	"git.zuoyebang.cc/ad/gocommons/const/fenxiaoconsts"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"go-hetu/services/afx"
	"math"
	"strings"
	"sync"
	"time"

	"go-hetu/api/api_wxqk"
	"go-hetu/components"
	"go-hetu/helpers"
	"go-hetu/models"
	"go-hetu/monitoring/action"
	"go-hetu/utils"

	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	libUtils "git.zuoyebang.cc/ad/gocommons/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/pool/gpool"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/now"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

/**
  两个任务：
	1. 定时缓存活动：查群控全部活动，过滤掉无数据的 10分钟一次
    2. 定时拉取明细：查活动明细，并记录无效渠道 5分钟一次
*/

const (
	keyUsedAct       = "tf:gohetu:ztfact_used:%s"     //有数据的活动
	keyNoUsedChannel = "tf:gohetu:ztfchan_no_used:%s" //无效渠道
	keyChannelData   = "tf:gohetu:ztfchan_%d:%s"      //渠道数据
)

func UpdateQkActCache(ctx *gin.Context) {
	var (
		dt      = ctx.Query("dt")
		refresh = ctx.Query("refresh")
	)
	if dt == "" {
		dt = time.Now().Format("20060102")
	}

	_ctx := ctx.Copy()
	go components.WithRecover(_ctx, func() {
		updateQkActCache(_ctx, dt, refresh)
	})

	base.RenderJsonSucc(ctx, "ok")
}

func updateQkActCache(ctx *gin.Context, dt, refresh string) {
	st := time.Now().UnixMilli()
	zlog.Info(ctx, "0转正数据 缓存有效活动...start...")

	var (
		ckUsedAct       = fmt.Sprintf(keyUsedAct, dt)
		ckNoUsedChannel = fmt.Sprintf(keyNoUsedChannel, dt)
	)
	if refresh == "1" {
		_, _ = helpers.RedisClient.Del(ctx, ckUsedAct)
		_, _ = helpers.RedisClient.Del(ctx, ckNoUsedChannel)
	}

	defer func() {
		expire := now.EndOfDay().Unix() - time.Now().Unix() + 3600*24
		_, _ = helpers.RedisClient.Expire(ctx, ckUsedAct, expire)
		_, _ = helpers.RedisClient.Expire(ctx, ckNoUsedChannel, expire)
	}()

	actResp, err := api_wxqk.GetAllActByTf(ctx)
	if err != nil {
		zlog.Errorf(ctx, "0转正数据 查询群控在投活动异常：%s", err.Error())
		return
	}
	actIds := actResp.ActIdList
	actStr, _ := jsoniter.MarshalToString(actIds)
	zlog.Infof(ctx, "0转正数据 群控返回在投活动：%s", actStr)

	var ids []string
	for _, id := range actIds {
		if ok, _ := hasDataByAct(ctx, id, dt); ok {
			idStr := cast.ToString(id)
			_, _ = helpers.RedisClient.SAdd(ctx, ckUsedAct, idStr)
			ids = append(ids, idStr)
		}
	}
	zlog.Infof(ctx, "0转正数据 有数据的在投活动：%s", strings.Join(ids, ","))

	zlog.Infof(ctx, "0转正数据 缓存有效活动...end... %d", time.Now().UnixMilli()-st)
}

func V3JobFetchQkActData(ctx *gin.Context) {
	var (
		actId = ctx.Query("actId")
		isFix = ctx.Query("isFix")
		dt    = ctx.Query("dt")
	)

	_ctx := ctx.Copy()
	// 修数
	if isFix == "1" && dt != "" {
		go components.WithRecover(_ctx, func() {
			fixData(ctx, "", dt)
		})
		base.RenderJsonSucc(ctx, "ok")
		return
	}
	if dt == "" {
		dt = time.Now().Format("20060102")
	}
	go components.WithRecover(_ctx, func() {
		fetchQkActV3(_ctx, dt, actId)
	})
	base.RenderJsonSucc(ctx, "ok")
}

func fetchQkActV3(ctx *gin.Context, dt, actId string) {
	st := time.Now().UnixMilli()
	zlog.Info(ctx, "0转正数据 拉取...start...")

	allActIds := getFromCache(ctx, dt)
	if env.GetRunEnv() == env.RunEnvTest {
		allActIds = []int{18613, 18607, 18611}
	}
	if actId != "" {
		allActIds = []int{cast.ToInt(actId)}
	}
	if len(allActIds) > 0 {
		zlog.Infof(ctx, "0转正数据 待拉取活动：%v", allActIds)

		// 查询要屏蔽的期次
		var periodFilter []string
		helpers.MysqlClient.WithContext(ctx).Model(&models.ZtfDataHide{}).Where("deleted=0").Distinct().Pluck("period", &periodFilter)

		var (
			wg    sync.WaitGroup
			gp, _ = gpool.NewPool(2)
		)
		defer gp.Release()

		actIds := utils.SplitSlice(allActIds, 10)
		for _, ids := range actIds {
			wg.Add(1)
			_ctx := ctx.Copy()
			_ids := ids
			_ = gp.Submit(func() {
				defer wg.Done()

				queryActDataV3(_ctx, dt, _ids, periodFilter)
			})
		}
		wg.Wait()
	}
	zlog.Infof(ctx, "0转正数据 拉取...end... %d", time.Now().UnixMilli()-st)
}

func queryActDataV3(ctx *gin.Context, dt string, actIds []int, periodFilter []string) {
	var instUqKeys []string
	st, et := getSETimeByDt(dt)
	var (
		ddList []models.ZtfDataDetail
		param  = &api_wxqk.GetDataParam{
			Pn:        1,
			Ps:        50,
			StartTime: st,
			EndTime:   et,
			ActIds:    actIds,
		}
	)
	for {
		//4.分页查询活动对应数据明细
		dataResp, err := api_wxqk.GetDataDetailByAct(ctx, param)
		if err != nil {
			zlog.Errorf(ctx, "0转正数据 查询活动数据异常：%s", err.Error())
			return
		}

		actBaseInfoMap := getBaseInfoByActIds(ctx, actIds)
		for _, d := range dataResp.List {
			if _, ok := actBaseInfoMap[d.ActId]; !ok {
				zlog.Infof(ctx, "活动查询失败：活动ID：%d，channelId：%d", d.ActId, d.ChannelId)
				continue
			}

			flowType := actBaseInfoMap[d.ActId].FlowType
			if flowType != 3 && flowType != 6 && flowType != 7 {
				zlog.Infof(ctx, "流量类型不符：活动ID：%d，channelId：%d，FlowType：%d", d.ActId, d.ChannelId, actBaseInfoMap[d.ActId].FlowType)
				continue
			}

			if d.LabelId != 5 && d.LabelId != 6 && d.LabelId != 7 {
				zlog.Infof(ctx, "学部不符：活动ID：%d，channelId：%d，LabelId：%d", d.ActId, d.ChannelId, d.LabelId)
				continue
			}

			if libUtils.ContainStr(periodFilter, d.SerialNumber) {
				zlog.Infof(ctx, "期次包含在内Continue：活动ID：%d，channelId：%d,期次：%s", d.ActId, d.ChannelId, d.SerialNumber)
				continue
			}

			instData, err := getInstDataByChannelV3(ctx, d.ChannelId, dt)
			if err != nil {
				zlog.Warnf(ctx, "0转正数据 反查渠道失败: %s, 活动ID：%d，channelId：%d", err.Error(), d.ActId, d.ChannelId)
				continue
			}
			flowTypeId := afx.GetFlowTypeId(d.LabelId, flowType)
			cv := 1.0
			if cm, ok := instData.CoefMap[fmt.Sprintf("%d_%s", flowTypeId, dt)]; ok {
				zlog.Infof(ctx, "InstId:%d, flowTypeId:%d, dt:%s useCoef:%d", instData.InstId, flowTypeId, dt, cm)
				cv = float64(cm) / float64(100)
			} else {
				zlog.Warnf(ctx, "0转正数据 学部[%s]无健康度系数, 活动ID：%d，channelId：%d", d.LabelName, d.ActId, d.ChannelId)
				continue
			}

			mdd := models.ZtfDataDetail{
				Dt:             dt,
				LastModifyTime: time.Now().Unix(),
				InstId:         int(instData.InstId),
				InstName:       instData.InstName,
				ChannelId:      d.ChannelId,
				ChannelName:    instData.ChannelName,
				ChannelCode:    instData.ChannelCode,
				ActId:          d.ActId,
				GradeDept:      d.LabelName,
				Period:         d.SerialNumber,
				VisitNum:       d.ChannelUv,
				ClickNum:       d.ClickTag,
				GetQrNum:       d.GainQrCode,
				ScanQrNum:      d.PressQrCode,
				LeadsNum:       d.WxGroupEnters,
				CoefVisitNum:   cast.ToInt(math.Ceil(float64(d.ChannelUv) * cv)),
				CoefClickNum:   cast.ToInt(math.Ceil(float64(d.ClickTag) * cv)),
				CoefGetQrNum:   cast.ToInt(math.Ceil(float64(d.GainQrCode) * cv)),
				CoefScanQrNum:  cast.ToInt(math.Ceil(float64(d.PressQrCode) * cv)),
				CoefLeadsNum:   cast.ToInt(math.Ceil(float64(d.WxGroupEnters) * cv)),
				TeamName:       actBaseInfoMap[d.ActId].TeamName,
			}

			mdd.FlowTypeId = flowTypeId

			flowTypeName := afx.GetFlowTypeName(d.LabelId, flowType)

			/*
				userRelation := getUserRelation(ctx, instData.InstId, flowTypeId)
				if userRelation != nil {
					mdd.Cadre = userRelation.Cadre
					mdd.AppId = userRelation.AppID
				} else {
					alertKey := fmt.Sprintf("ztf_alert_%d_%s_%d", instData.InstId, instData.ChannelCode, flowTypeId)
					res, _ := helpers.RedisClient.Get(ctx, alertKey)
					if string(res) == "" {
						action.DingdingAlert(ctx, int64(instData.InstId), instData.ChannelCode, flowTypeName)
						_ = helpers.RedisClient.Set(ctx, alertKey, "1", 60*60*3)
					}
				}
			*/

			// 归属人归因, 伏羲机构报备-学部，伏羲机构报备-业务线，伏羲机构报备-新推广产品, 伏羲报备机构id
			var promoteGradeDept, promoteBusiness, promoteLv1New int
			fxInstRegisterList := make([]tfmodels.QudaoInstRegister, 0)
			promoteGradeDept = components.GetPromoteGradeDept(mdd.GradeDept)        // 根据 GradeDept 获取 伏羲机构报备-学部
			promoteBusiness = components.GetPromoteBusinessByTeamName(mdd.TeamName) // 根据 teamName 获取 伏羲机构报备-业务线
			promoteLv1New, _ = components.QkFlowTypePromoteLv1NewMap[int(flowType)] // 根据 flowType 获取 伏羲机构报备-新推广产品

			if instData.FxInstId <= 0 || promoteGradeDept <= 0 || promoteBusiness <= 0 || promoteLv1New <= 0 {
				zlog.Errorf(ctx, fmt.Sprintf("0转正数据-伏羲机构报备的fxInstId/学部/业务线/新推广产品为空, actId=%d, channelId=%d,instId=%v, fxInstId=%v, promoteGradeDept=%v, promoteBusiness=%v, promoteLv1New=%v, err=%v",
					d.ActId, d.ChannelId, instData.InstId, instData.FxInstId, promoteGradeDept, promoteBusiness, promoteLv1New, err))
			} else {
				// 查伏羲机构报备表
				err = helpers.MysqlClientQD.WithContext(ctx).Table(tfmodels.TblQudaoInstRegister).
					Where("inst_id = ? and promote_grade_dept = ? and promote_business = ?  and promote_lv1_new = ? and run_status in ?",
						instData.FxInstId, promoteGradeDept, promoteBusiness, promoteLv1New, []int{fenxiaoconsts.RUN_STATUS_RUNNING, fenxiaoconsts.RUN_STATUS_DEFER}).
					Order("id desc").Find(&fxInstRegisterList).Error
				if err != nil {
					zlog.Errorf(ctx, fmt.Sprintf("0转正数据-查询db错误, actId=%d，channelId=%d,instId=%v, fxInstId=%v, promoteGradeDept=%v, promoteBusiness=%v, promoteLv1New=%v, err=%v",
						d.ActId, d.ChannelId, instData.InstId, instData.FxInstId, promoteGradeDept, promoteBusiness, promoteLv1New, err))
					continue
				}
			}

			if len(fxInstRegisterList) > 0 {
				mdd.Cadre = fxInstRegisterList[0].Cadre
				mdd.AppId = int64(instData.AppId)
			} else {
				msg := fmt.Sprintf("0转正订单匹配归属人cadre失败-instId=%v, channelCode=%v, flowTypeName=%v, fxInstId=%v, gradeDept=%v, teamName=%v, flowType=%v, promoteGradeDept=%v, promoteBusiness=%v, promoteLv1New=%v",
					instData.InstId, instData.ChannelCode, flowTypeName, instData.FxInstId, mdd.GradeDept, mdd.TeamName, flowType, promoteGradeDept, promoteBusiness, promoteLv1New)
				zlog.Warnf(ctx, msg)
				alertKey := fmt.Sprintf("ztf_alert_%d_%s_%d_%v_%v", instData.InstId, instData.ChannelCode, flowTypeId, mdd.GradeDept, mdd.TeamName)
				res, errRedis := helpers.RedisClient.Get(ctx, alertKey)
				if errRedis != nil {
					zlog.Warnf(ctx, fmt.Sprintf("queryActDataV3-errRedis=%v", errRedis.Error()))
				}
				if string(res) == "" || env.GetRunEnv() == env.RunEnvTest {
					action.DingdingAlert(ctx, int64(instData.InstId), instData.ChannelCode, flowTypeName, msg)
					_ = helpers.RedisClient.Set(ctx, alertKey, "1", 60*60*3)
				}
			}

			//防分页排序变化，导致数据重复
			uk := fmt.Sprintf("%d-%s-%d-%d-%s-%s", mdd.InstId, mdd.Dt, mdd.ActId, mdd.ChannelId, mdd.GradeDept, mdd.Period)
			if libUtils.ContainStr(instUqKeys, uk) {
				zlog.Infof(ctx, "0转正数据 代理商拉取数据重复：%s,活动ID：%d，channelId：%d", uk, d.ActId, d.ChannelId)
				continue
			} else {
				instUqKeys = append(instUqKeys, uk)
				ddList = append(ddList, mdd)
			}
		}
		zlog.Infof(ctx, "createInBatch_ddList:%s", helpers.ToString(ddList))

		if dataResp.Meta.Total <= int64(param.Pn*param.Ps) {
			break
		}
		param.Pn = param.Pn + 1
	}

	if len(ddList) > 0 {
		helpers.MysqlClient.WithContext(ctx).Where("dt=? and act_id in ?", dt, actIds).Delete(&models.ZtfDataDetail{})
		if dbErr := helpers.MysqlClient.WithContext(ctx).CreateInBatches(&ddList, 200).Error; dbErr != nil {
			zlog.Errorf(ctx, "批量插入0转正详细表失败:%+v", dbErr)
		}
	}
}

type ActBaseInfo struct {
	TeamName string
	FlowType int64
}

func getBaseInfoByActIds(ctx *gin.Context, actIds []int) map[int]ActBaseInfo {
	res := make(map[int]ActBaseInfo)
	activityInfo, err := api_wxqk.ActivityBaseInfo(ctx, actIds)
	if err != nil {
		zlog.Warnf(ctx, "0转正数据 查询活动数据异常：%s", err.Error())
		return res
	}
	activityInfo.Get("data.dataList").ForEach(func(key, value gjson.Result) bool {
		actId := cast.ToInt(value.Get("activityId").Int())
		teamName := value.Get("teamName").String()
		flowType := value.Get("flowType").Int()
		res[actId] = ActBaseInfo{TeamName: teamName, FlowType: flowType}
		return true
	})
	return res
}

func getUserRelation(ctx *gin.Context, instId uint64, flowTypeId int64) (userRelation *tfmodels.TblAFXUserRelation) {
	userRelationCond := models.NewAfxUserRelationCond()
	userRelationCond.WithInstID(int64(instId))
	userRelationCond.WithRelationType(5)
	userRelationCond.WithRelationValue(cast.ToString(flowTypeId))
	userRelationCond.WithStatuses([]int{models.TblUserRelationStatusUsed})
	repo := models.NewAfxUserRelationRepo(dbcontext.New(helpers.MysqlClient))
	userRelationList, err := repo.SelectByParams(ctx, userRelationCond)
	if err != nil || len(userRelationList) == 0 {
		if err == nil {
			err = fmt.Errorf("[getUserRelation] 活动查询失败")
		}
		zlog.Warnf(ctx, "[hetu]getUserRelation failed, userRelationCond=[%v], err=[%+v]", userRelationCond, err)
		return nil
	}
	return &userRelationList[0]
}

func getFromCache(ctx *gin.Context, dt string) []int {
	var (
		actIds []int
	)
	// 有数据活动
	if bytes, err := helpers.RedisClient.SMembers(ctx, fmt.Sprintf(keyUsedAct, dt)); err == nil && len(bytes) > 0 {
		for _, bts := range bytes {
			actIds = append(actIds, cast.ToInt(string(bts)))
		}
	}
	return actIds
}

func getInstDataByChannelV3(ctx *gin.Context, channelId int, dt string) (*channelData, error) {
	ck := fmt.Sprintf(keyChannelData, channelId, dt)
	if t, err := helpers.RedisClient.Get(ctx, ck); err == nil && len(t) > 0 {
		var cd channelData
		_ = jsoniter.Unmarshal(t, &cd)
		if cd.FxInstId > 0 {
			return &cd, nil
		}
	}

	var channel models.ZtfChannel
	helpers.MysqlClient.WithContext(ctx).Where("channel_id = ?", channelId).Take(&channel)
	if channel.Id == 0 {
		return nil, errors.New(fmt.Sprintf("渠道未查到: %d", channelId))
	}
	var inst models.AfxInst
	helpers.MysqlClient.WithContext(ctx).Where("ztf_can_channel = 1 and ztf_channel_code =?", channel.ChannelCode).Take(&inst)
	if inst.Id == 0 {
		return nil, errors.New(fmt.Sprintf("渠道未查到代理商: %d", channelId))
	}

	cm, err := getCoefByInstV3(ctx, inst.Id, dt)
	if err != nil {
		return nil, err
	}
	cd := &channelData{
		InstId:      inst.Id,
		InstName:    inst.Name,
		FxInstId:    inst.FxInstId,
		AppId:       inst.AppId,
		ChannelId:   channel.ChannelId,
		ChannelName: channel.ChannelName,
		ChannelCode: channel.ChannelCode,
		CoefMap:     cm,
	}
	cdStr, _ := jsoniter.MarshalToString(cd)
	_ = helpers.RedisClient.SetEx(ctx, ck, cdStr, 3600)
	return cd, nil
}

func getCoefByInstV3(ctx *gin.Context, instId uint64, dt string) (map[string]int, error) {
	coefMap := make(map[string]int)
	var coefList []models.ZtfHealthCoef
	helpers.MysqlClient.WithContext(ctx).Where("inst_id=?", instId).Find(&coefList)
	for _, coef := range coefList {
		var coefDateConf []struct {
			Dt    string `json:"dt"`
			Value int    `json:"coefficient"`
		}
		_ = jsoniter.Unmarshal([]byte(coef.CoefDateConf), &coefDateConf)
		for _, cdc := range coefDateConf {
			if dt >= cdc.Dt {
				coefMap[fmt.Sprintf("%d_%s", coef.FlowTypeId, dt)] = cdc.Value
				break
			}
		}
	}
	if len(coefMap) == 0 {
		return nil, errors.New(fmt.Sprintf("代理商未查到健康系数：%d", instId))
	}
	zlog.Infof(ctx, "代理机构[%d]健康度系数: %v", instId, coefMap)
	return coefMap, nil
}

func hasDataByAct(ctx *gin.Context, actId int, dt string) (bool, error) {
	st, et := getSETimeByDt(dt)
	param := &api_wxqk.GetDataParam{
		Pn:        1,
		Ps:        5,
		StartTime: st,
		EndTime:   et,
		ActIds:    []int{actId},
	}
	resp, err := api_wxqk.GetDataDetailByAct(ctx, param)
	if err != nil {
		zlog.Errorf(ctx, "0转正数据 查询活动明细数据异常：%s", err.Error())
		return false, err
	}
	return len(resp.List) > 0, nil
}

func getSETimeByDt(dt string) (s, e int64) {
	st, _ := time.ParseInLocation("20060102 15:04:05", fmt.Sprintf("%s %s", dt, "00:00:00"), time.Local)
	et, _ := time.ParseInLocation("20060102 15:04:05", fmt.Sprintf("%s %s", dt, "23:59:59"), time.Local)
	return st.Unix(), et.Unix()
}

func minus(a []int, b []int) []int {
	var inter []int
	mp := make(map[int]bool)
	for _, s := range a {
		if _, ok := mp[s]; !ok {
			mp[s] = true
		}
	}
	for _, s := range b {
		if _, ok := mp[s]; ok {
			delete(mp, s)
		}
	}
	for key := range mp {
		inter = append(inter, key)
	}
	return inter
}
