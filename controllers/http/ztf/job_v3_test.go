package ztf

import (
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"go-hetu/models"
	"testing"
)

var ctx = &gin.Context{}

func TestMain(m *testing.M) {
	env.SetRootPath("../../..")
	helpers.PreInit()
	defer helpers.Clear()

	helpers.InitMysql()
	helpers.InitRedis()
	m.Run()
}

func Test_fixData(t *testing.T) {
	fixData(ctx, "11233", "20231109")
}

func Test_parallelProcess(t *testing.T) {
	insts := []models.AfxInst{
		{Id: 11233, ZtfChannelCode: "A"},
		{Id: 11226, ZtfChannelCode: "区FJRZ"},
	}
	parallelProcess(ctx, insts, "20231111")
}

func Test_fetchQkDataV3(t *testing.T) {
	actId := "14355"
	//actId := "14259"

	fetchQkActV3(ctx, "20231121", actId)
}

func Test_QkActUpdateCache(t *testing.T) {
	UpdateQkActCache(ctx)
}

func Test_queryActDataV3(t *testing.T){
	actIds := []int{21127}
	queryActDataV3(ctx, "20250425", actIds, []string{})
}