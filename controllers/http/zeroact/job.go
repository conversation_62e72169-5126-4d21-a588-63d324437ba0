package zeroact

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"github.com/gin-gonic/gin"
	"go-hetu/components"
	"go-hetu/helpers"
	"go-hetu/models"
	"go-hetu/services/zeroact"
)

const (
	exeStatusWaiting = 1 //待执行
)

func JobDownloadTaskExec(ctx *gin.Context) {
	var taskList []models.ZeroActivityDownload
	helpers.MysqlClient.WithContext(ctx).Where("status=? and deleted=0", exeStatusWaiting).Find(&taskList).Limit(500)
	if len(taskList) == 0 {
		base.RenderJsonSucc(ctx, "no task")
		return
	}

	_ctx := ctx.Copy()
	for _, task := range taskList {
		go components.WithRecover(_ctx, func() {
			zeroact.ExecDownloadTaskByAct(_ctx, task)
		})
	}
	base.RenderJsonSucc(ctx, "ok")
}
