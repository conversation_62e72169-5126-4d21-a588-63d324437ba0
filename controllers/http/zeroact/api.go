package zeroact

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go-hetu/components"
	"go-hetu/models"
	"go-hetu/services/zeroact"
	"go-hetu/services/zeroact/data"
)

type resource struct {
	service zeroact.Service
}

func RegisterHandlers(zeroRouter *gin.RouterGroup) {
	service := zeroact.NewService()
	r := resource{service: service}
	// 添加0转正活动 http://yapi.zuoyebang.cc/project/5845/interface/api/691978 go-hetu/zeromanage/add
	zeroRouter.POST("/add", r.Add)
	zeroRouter.POST("/update", r.Update)
	// 活动列表搜索 http://yapi.zuoyebang.cc/project/5845/interface/api/691987 go-hetu/zeromanage/search
	zeroRouter.GET("/search", r.Search)
	zeroRouter.GET("/info", r.GetInfo)
	// 删除活动 http://yapi.zuoyebang.cc/project/5845/interface/api/691996 go-hetu/zeromanage/delete
	zeroRouter.POST("/delete", r.Delete)
	// 下载活动渠道 http://yapi.zuoyebang.cc/project/5845/interface/api/692005 go-hetu/zeromanage/downchannels
	zeroRouter.GET("/downchannels", r.DownChannels)

	zeroRouter.POST("/listactchannel", ListActChannel)
	zeroRouter.POST("/generateurl", GenerateUrl)
	zeroRouter.POST("/download_add", AddDownloadTask)
	zeroRouter.POST("/download_list", PageDownloadTask)
	zeroRouter.POST("/download_del", DelDownloadTask)

}

func (r resource) Add(ctx *gin.Context) {
	var param data.AddActivityParam
	if err := ctx.ShouldBindJSON(&param); err != nil {
		zlog.Warnf(ctx, "[Add] ShouldBindJSON failed: param=[%+v], err=[%v]", param, err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	if err := r.service.AddActivity(ctx, param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) Update(ctx *gin.Context) {
	var param data.UpdateActivityParam
	if err := ctx.ShouldBindJSON(&param); err != nil {
		zlog.Warnf(ctx, "[Add] ShouldBindJSON failed: param=[%+v], err=[%v]", param, err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	if err := r.service.UpdateActivity(ctx, param); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) Search(ctx *gin.Context) {
	var param data.SearchActivityParam
	err := ctx.ShouldBind(&param)
	if err != nil {
		zlog.Warnf(ctx, "[Search] ShouldBind failed:param=[%+v], err=[%v]", param, err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	res, err := r.service.SearchActivity(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, res)
}

func (r resource) GetInfo(ctx *gin.Context) {
	var param data.GetActivityParam
	err := ctx.ShouldBind(&param)
	if err != nil {
		zlog.Warnf(ctx, "[GetInfo] ShouldBind failed:param=[%+v], err=[%v]", param, err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	res, err := r.service.GetActivity(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}
	base.RenderJsonSucc(ctx, res)
}

func (r resource) Delete(ctx *gin.Context) {
	param := struct {
		ActyID models.ID `json:"actyId" binding:"required"`
	}{}
	if err := ctx.ShouldBindJSON(&param); err != nil {
		zlog.Warnf(ctx, "[Delete] ctx.ShouldBindJSON failed: param=[%+v], err=[%v]", param, err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	if err := r.service.DeleteActivity(ctx, param.ActyID); err != nil {
		base.RenderJsonFail(ctx, err)
		return
	}

	base.RenderJsonSucc(ctx, gin.H{})
}

func (r resource) DownChannels(ctx *gin.Context) {
	param := struct {
		ActyID models.ID `form:"actyId"`
	}{}
	if err := ctx.ShouldBind(&param); err != nil {
		zlog.Warnf(ctx, "[DownChannels] ctx.ShouldBind failed: err=[%v]", err)
		err = components.ErrorParamInvalid
		base.RenderJsonFail(ctx, err)
		return
	}
	if err := r.service.DownloadChannel(ctx, param.ActyID); err != nil {
		base.RenderJsonFail(ctx, err)
	}
}

func ListActChannel(ctx *gin.Context) {
	var req data.ListChannelParam
	if err := ctx.ShouldBindJSON(&req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if pager, err := zeroact.ListActChannel(ctx, req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, err)
	} else {
		base.RenderJsonSucc(ctx, pager)
	}
}

func GenerateUrl(ctx *gin.Context) {
	var req data.GenUrlParam
	if err := ctx.ShouldBindJSON(&req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if result, err := zeroact.GenerateUrl(ctx, req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, err)
	} else {
		base.RenderJsonSucc(ctx, result)
	}
}

func PageDownloadTask(ctx *gin.Context) {
	var req struct {
		Pn int `json:"pn"`
		Ps int `json:"ps"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if pager, err := zeroact.PageListDownloadTask(ctx, req.Pn, req.Ps); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, err)
	} else {
		base.RenderJsonSucc(ctx, pager)
	}
}

func AddDownloadTask(ctx *gin.Context) {
	var req data.ActChannelParam
	if err := ctx.ShouldBindJSON(&req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if err := zeroact.AddDownloadTask(ctx, req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, err)
	} else {
		base.RenderJsonSucc(ctx, "ok")
	}
}

func DelDownloadTask(ctx *gin.Context) {
	var req struct {
		Id int64 `json:"id"`
	}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, components.ErrorParamInvalid)
		return
	}
	if err := zeroact.DelDownloadTask(ctx, req.Id); err != nil {
		zlog.Warn(ctx, err.Error())
		base.RenderJsonFail(ctx, err)
	} else {
		base.RenderJsonSucc(ctx, "ok")
	}
}
