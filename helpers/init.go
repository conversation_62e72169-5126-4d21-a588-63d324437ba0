package helpers

import (
	"go-hetu/conf"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

func Init(engine *gin.Engine) {
	PreInit()
	InitResource(engine)
}

// 基础资源（必须）
func PreInit() {
	// 用于日志中展示模块的名字，开发环境需要手动指定，容器中无需手动指定
	env.SetAppName("go-hetu")

	// 配置加载
	conf.InitConf()

	// 日志初始化
	zlog.InitLog(conf.BasicConf.Log)

	// 如果对外输出的 errNo/errMsg 与框架中默认定义不同，可在此修改json tag
	initRender()
}

func Clear() {
	Release()
	// 服务结束时的清理工作，对应 Init() 初始化的资源
	zlog.CloseLogger()
}

// web服务启动所需init的资源
func InitResource(engine *gin.Engine) {
	InitRedis()
	InitMysql()
	InitIps()
	InitZos()
	InitJob(engine)
	InitKms()
	InitRmq()
}

func Release() {
	CloseMysql()
	CloseRedis()
	CloseRocketMq()
}
