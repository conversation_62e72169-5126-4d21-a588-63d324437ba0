package data

// 一级代理商管理-列表&搜索
type (
	AfxOrganListReq struct {
		Pn         int      `json:"pn"    form:"pn"    binding:"required"` // pageNumber
		Rn         int      `json:"rn"    form:"rn"    binding:"required"` // rowNumber
		AppIDs     []int64  `json:"appId" form:"appId[]"`                  // 应用方
		Id         int64    `json:"id"    form:"id"`                       // 代理机构id, tblAFXInst.ID
		Name       string   `json:"name"  form:"name"`                     // 代理机构名称, tblAFXInst.Name
		Cadre      []string `json:"cadre" form:"cadre[]"`                  // 归属人
		State      int      `json:"state" form:"state"`                    // 代理机构状态，1-启用，2-停用
		CreateUser string   `json:"createUser" form:"createUser[]"`        // 创建人 tblAFXInst.Cadre
	}

	AfxOrganListResp struct {
		Pn    int            `json:"pn"`
		Total int64          `json:"total"`
		List  []AfxOrganList `json:"list"`
	}

	AfxOrganList struct {
		ID           int64                  `json:"id"`
		AppID        int                    `json:"appId"`        // 应用方id
		AppName      string                 `json:"appName"`      // 应用方名称
		Name         string                 `json:"name"`         // 代理商机构名称
		CreateUid    int64                  `json:"createUid"`    // 创建人uid
		Cadre        string                 `json:"cadre"`        // 归属人
		CreateTime   string                 `json:"createTime"`   // 机构创建时间
		UpdateTime   int64                  `json:"updateTime"`   // 机构更新时间
		State        int                    `json:"state"`        // 1-启用，2-停用
		FxInstID     int64                  `json:"fxInstId"`     // 伏羲代理商机构id
		CreateName   string                 `json:"createName"`   // 创建人名称
		CreateUser   string                 `json:"createUser"`   // 创建人名称
		SaleTypeInfo []*SaveAfxSaleTypeInfo `json:"saleTypeInfo"` // 售卖信息
		EXTData      []interface{}          `json:"extData"`      //
	}

	AfxSaleTypeInfo struct {
		SaleModule  interface{} `json:"saleModule"`
		SaleTypeLv1 interface{} `json:"saleTypeLv1"`
		SaleTypeLv2 interface{} `json:"saleTypeLv2"`
		RuleKey     string      `json:"ruleKey"`
	}
)

// 通用-获取用户手机号
type (
	AfxDeUserInfoReq struct {
		Type       string `form:"type"`       // inst or user
		Id         int64  `form:"id"`         // instID or userID
		SystemType string `form:"systemType"` // hetu or afxmis
	}

	AfxDeUserInfoResp struct {
		Mobile   int64  `json:"mobile"`
		Email    string `json:"email"`
		Nickname string `json:"nickname"`
	}
)

// 代理商管理-列表
type (
	AfxUserListReq struct {
		Pn      int    `form:"pn" json:"pn"`
		Rn      int    `form:"rn" json:"rn"`
		AppID   string `form:"appId" json:"appId"`
		OrganId int64  `form:"organId" json:"organId"` // 河图的代理商机构id
		Cadre   string `form:"cadre" json:"cadre"`     // 代理机构归属人，可能会多个
		RoleId  int    `form:"roleId" json:"roleId"`   // 11-一级代理商，12-二级代理商，13-三级代理商
		Name    string `form:"name" json:"name"`       // 用户名称
		Id      int64  `form:"id" json:"id"`           // 用户id
		Mobile  string `form:"mobile" json:"mobile"`   // 手机号码-明文
	}

	AfxUserListResp struct {
		Pn    int           `json:"pn"`
		Total int64         `json:"total"`
		List  []AfxUserList `json:"list"`
	}

	AfxUserList struct {
		ID                  int64       `json:"id"`
		AppID               int64       `json:"appId"`
		AppName             string      `json:"appName"`
		Uid                 int64       `json:"uid"`
		Nickname            string      `json:"nickname"`
		Pid                 int64       `json:"pid"`
		Relation            string      `json:"relation"`
		AID                 int64       `json:"aId"`
		RoleID              int         `json:"roleId"`
		Status              int         `json:"status"`
		Deleted             int         `json:"deleted"`
		InstID              int64       `json:"instId"`
		Cadre               string      `json:"cadre"`
		ModelType           int         `json:"modelType"`
		CooperationType     int         `json:"cooperationType"`
		Email               string      `json:"email"`
		CreateTime          string      `json:"createTime"`
		UpdateTime          int64       `json:"updateTime"`
		EXTData             interface{} `json:"extData"`
		NameEncry           int         `json:"nameEncry"`
		Mobile              string      `json:"mobile"`
		OrganID             int64       `json:"organId"`
		OrganName           string      `json:"organName"`
		StatusName          string      `json:"statusName"`
		Role                string      `json:"role"`
		Extend              string      `json:"extend"`
		ModelTypeName       string      `json:"modelTypeName"`
		CooperationTypeName string      `json:"cooperationTypeName"`
		SuperiorName        string      `json:"superiorName"`
		AllowView           bool        `json:"allowView"`
	}
)

// 一级代理商管理-保存&编辑
type (
	AfxOrganSaveReq struct {
		ID             int64                 `json:"id"`                                           // 新增时为0，更新时为对应的 tblAFXUser.inst_id
		HetuInstID     int64                 `json:"hetuInstId"`                                   // 新增且新增归属人时使用
		InstID         int64                 `json:"instId"       binding:"required"`              // 伏羲代理商机构id tblAFXInst.fx_inst_id
		OrganName      string                `json:"organName"    binding:"required"`              // 伏羲代理商机构名称
		AppID          int64                 `json:"appId"        binding:"required"`              // 应用方id
		PromoteValue   []int                 `json:"promoteValue"`                                 // 推广一级的值
		Mobile         string                `json:"mobile"       binding:"required,max=11,min=1"` // 一级代理商手机号
		NickName       string                `json:"nickName"     binding:"required,max=20,min=1"` // 一级代理商管理员名称
		SaleTypeInfo   []SaveAfxSaleTypeInfo `json:"saleTypeInfo" binding:"required,gt=0"`         // 销售情况
		UID            int64                 `json:"uid"`                                          // 用户uid
		Cadre          string                `json:"cadre"`                                        // 归属人
		CreateType     int                   `json:"createType"`                                   // 新增数据类型
		ExtData        []ExtDataStruct       `json:"extData"`                                      // 附加属性
		PromoteList    []PromoteItem         `json:"promoteList" binding:"required"`               // 伏羲报备推广一级扩展的字段：业务线，学部，新推广产品
		ComplexPromote map[string]int        `json:"complexPromote"`                               // 业务线|学部|新推广产品, 根据入参promoteList拼出来的值
	}

	ExtDataStruct struct {
		Extend string `json:"extend"`
	}

	SaveAfxSaleTypeInfo struct {
		SaleModule  int    `json:"saleModule"`
		SaleTypeLv1 int    `json:"saleTypeLv1"`
		SaleTypeLv2 int    `json:"saleTypeLv2"`
		RuleKey     string `json:"ruleKey"`
	}

	AfxOrganSaveResp struct {
		Id int64 `json:"id"`
	}
)

// 通用-获取筛选项中内容
type (
	AfxSelectListReq struct {
		DataType []string `form:"dataType[]"`
	}

	AfxSelectListResp struct {
		App           map[int64]string  `json:"app"`           // 应用列表
		OrganContacts map[string]string `json:"organContacts"` // 联系人列表
	}
)

// 通用-获取售卖相关筛选项
type (
	AfxSaleTypeInfoResp struct {
		SaleType   []SaleType   `json:"saleType"`
		SaleModule []SaleModule `json:"saleModule"`
	}

	SaleType struct {
		Code     string       `json:"code"`
		Value    string       `json:"value"`
		Children []SaleModule `json:"children"`
	}

	SaleModule struct {
		Code  string `json:"code"`
		Value string `json:"value"`
	}
)

// 一级代理商管理-获取已报备的代理机构
type (
	AfxRegisterInstReq struct {
		Keyword string `form:"keyword"`
		AppId   int64  `form:"appId"`
		InstID  int64  `form:"instId"`
	}

	AfxRegisterInstResp struct {
		List []*AfxRegisterInstRespItem `json:"list"`
	}
	AfxRegisterInstRespItem struct {
		FxInstId              int64  `json:"fxInstId"`
		Name                  string `json:"name"`
		Cadre                 string `json:"cadre"`
		PromoteValue          int64  `json:"promoteLv1"`
		PromoteLabel          string `json:"promoteLv1Label"`
		PromoteBusiness       int    `json:"promoteBusiness"`  // 伏羲机构报备-业务线
		PromoteGradeDept      int    `json:"promoteGradeDept"` // 伏羲机构报备-学部
		PromoteLv1New         int    `json:"promoteLv1New"`    // 伏羲机构报备-新推广产品
		PromoteBusinessLabel  string `json:"promoteBusinessLabel"`
		PromoteGradeDeptLabel string `json:"promoteGradeDeptLabel"`
		PromoteLv1NewLabel    string `json:"promoteLv1NewLabel"`
		State                 int    `json:"state"`
		StateName             string `json:"stateName"`
		Added                 int    `json:"added"` // 是否已在河图添加该推广产品一级：1已添加， 0未添加
	}
)

// 一级代理商管理-获取全部代理商机构信息
type (
	AfxFxRegisterInstReq struct {
		InstID int64 `form:"instId"`
		AppId  int64 `form:"appId"`
		IsGZ   int   `form:"isGz"`
	}

	AfxFxRegisterInstResp struct {
		List []*AfxRegisterInstRespItem `json:"list"`
	}
)

type (
	AfxUserInfoReq struct {
		InstID int64 `form:"instId"`
		AppID  int64 `form:"appId"`
	}

	AfxUserInfoResp struct {
		Mobile   string `json:"mobile"`
		Nickname string `json:"nickname"`
	}
)

type (
	AfxCheckAgentReq struct {
		FxInstId int64 `form:"fxInstId" binding:"required"` // 伏羲代理机构id
		AppId    int64 `form:"appId" binding:"required"`    // 应用方id
	}

	AfxCheckAgentResp struct {
		List []AfxCheckAgentRespItem `json:"list"` // 可能出现列表的情况
	}
	AfxCheckAgentRespItem struct {
		Uid       int64  `json:"uid"`       // uid
		NickName  string `json:"nickName"`  // 一级代理商名字
		Mobile    string `json:"mobile"`    // 一级代理商手机号
		IsSameApp int    `json:"isSameApp"` // 是否在同一个应用方
		InstID    int64  `json:"instId"`    // tblAFXInst.id
	}
)

type (
	AfxConfListReq struct {
		Pn int `json:"pn" form:"pn"`
		Ps int `json:"ps" form:"ps"`
	}

	AfxConfListResp struct {
		List  []AfxConfListRespItem `json:"list"`
		Total int64                 `json:"total"`
		Pn    int                   `json:"pn"`
	}
	AfxConfListRespItem struct {
		ID           int64             `json:"id"`
		PromoteValue int64             `json:"promoteValue"`
		PromoteLabel string            `json:"promoteLabel"`
		Type         int64             `json:"type"`
		TypeValue    string            `json:"typeValue"`
		TypeLabel    string            `json:"typeLabel"`
		TypeList     []AfxConfTypeList `json:"typeList"`
		Sort         int               `json:"-"`
	}

	AfxConfFilterListResp struct {
		Promote  []QdmisKVItem `json:"promote"`
		Grade    []QdmisKVItem `json:"grade"`
		Subject  []QdmisKVItem `json:"subject"`
		Team     []QdmisKVItem `json:"team"`
		FlowType []QdmisKVItem `json:"flowType"`
		Other    []QdmisKVItem `json:"other"`
	}

	QdmisKVItem struct {
		Value    int           `json:"value"`
		Label    string        `json:"label"`
		Children []QdmisKVItem `json:"children"`
	}

	AfxConfAddReq struct {
		PromoteValue int               `json:"promoteValue" binding:"required"` // 产品推广一级对应的id
		PromoteLabel string            `json:"promoteLabel" binding:"required"`
		Type         int               `json:"type"         binding:"required"` // 1-学部，2-学科，3-招生团队，4-其他
		Id           int64             `json:"id"`
		TypeList     []AfxConfTypeList `json:"typeList"`
	}

	AfxConfTypeList struct {
		TypeValue int    `json:"typeValue"`
		TypeLabel string `json:"typeLabel"`
	}
	OptionItem struct {
		Key   any `json:"key"`
		Value any `json:"value"`
	}
	OptionsResp struct {
		PromoteLv1       []OptionItem `json:"promoteLv1"`
		Tag              []OptionItem `json:"tag"`
		PromoteBusiness  []OptionItem `json:"promoteBusiness"`
		PromoteGradeDept []OptionItem `json:"promoteGradeDept"`
		PromoteLv1New    []OptionItem `json:"promoteLv1New"`
	}
)

type (
	AfxGetCadreReq struct {
		Key string `form:"key"`
	}
)

// 消费rmq得到入参，用于释放公海后设置失效
type (
	RmqAfxReleaseInstReq struct {
		InstId     []int64  `json:"instId"`     // 伏羲inst_id
		PromoteLv1 []int    `json:"promoteLv1"` // 推广一级
		Cadre      []string `json:"cadre"`      // 当前报备的负责人
	}

	RmqAfxReleaseInstNewReq struct {
		InstId           int64 `json:"instId"`           // 伏羲inst_id
		PromoteLv1       int   `json:"promoteLv1"`       // 推广产品
		PromoteBusiness  int   `json:"promoteBusiness"`  // 业务线
		PromoteGradeDept int   `json:"promoteGradeDept"` // 学部
		PromoteLv1New    int   `json:"promoteLv1New"`    // 新推广产品
	}

	RmqAfxEditInstNameReq struct {
		InstId  int64  `json:"instId"`  // 伏羲inst_id,对应tblAFXInst.fx_inst_id
		NewName string `json:"newName"` // 新名称
	}
)

type SelectListRequest struct {
	EditId            int      `form:"editId"`
	DataType          []string `form:"dataType[]"`
	ProjectLv1Name    string   `form:"projectLv1Name"`
	PromoteLv1        string   `form:"promoteLv1"`
	PromoteBusiness   int      `form:"promoteBusiness"`   // 伏羲机构报备-业务线 根据应用方映射
	PromoteGradeDepts string   `form:"promoteGradeDepts"` // 伏羲机构报备-学部
	PromoteLv1New     int      `form:"promoteLv1New"`     // 伏羲机构报备-新推广产品
	ChannelLabels     string   `form:"channelLabels"`     // 渠道标签, 逗号隔开
	AgentKeyIsId      int      `form:"agentKeyIsId"`
	AppID             string   `form:"appId"`
	OrganProvince     string   `form:"organProvince"`
	OrganCity         string   `form:"organCity"`
	OrganArea         string   `form:"organArea"`
	OrganName         string   `form:"organName"`
	Cadre             string   `form:"cadre"`
	AgentName         string   `form:"agentName"`
	RoleId            int      `form:"roleId"`
	AgentShow         int      `form:"agentShow"` // angentList 参数时， agentShow = 2 展示tblAFXInst.name
	ApiSource         int      `form:"apiSource"` // 目前只有351 DYZT 低幼中台接口可以使用
}

var BusinessLineMap = map[int]string{
	4:    "直播课",
	101:  "小鹿素养",
	102:  "鸭鸭英语",
	103:  "鸭鸭语文",
	121:  "鸭鸭低幼",
	126:  "帮帮识字",
	130:  "教辅业务",
	138:  "碳氧智能",
	149:  "GX",
	155:  "HM",
	9999: "高中市场",
}

// 伏羲报备推广一级扩展的字段：业务线，学部，新推广产品
type PromoteItem struct {
	PromoteLv1       int `json:"promoteLv1"`       // 伏羲机构报备-旧推广产品
	PromoteBusiness  int `json:"promoteBusiness"`  // 伏羲机构报备-业务线
	PromoteGradeDept int `json:"promoteGradeDept"` // 伏羲机构报备-学部
	PromoteLv1New    int `json:"promoteLv1New"`    // 伏羲机构报备-新推广产品
}

type AddInstShutConfig struct {
	InstIDs      []int64 `json:"instIds"`
	OperatorType int     `json:"operatorType"`
}

type AddActShutConfig struct {
	ActID int64 `json:"actId"`
}
