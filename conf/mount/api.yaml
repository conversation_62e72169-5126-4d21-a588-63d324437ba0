upms:
  service: upms
  domain: http://sellmis-base-cc.suanshubang.cc
  timeout: 3s
  retry: 1

wxqk:
  service: wxqk
#  domain: http://wxqk.laxin-qk-base-dd.suanshubang.cc
#  domain: http://wxqk-svc.laxin-qk:8080
  domain: http://wxtools.zuoyebang.cc #wxqk.zuoyebang.cc
  timeout: 3s
  retry: 1

wxtools:
  service: wxtools
  domain: https://wxtools.zuoyebang.cc
  timeout: 3s
  retry: 1

ucloud:
  service: "ucloud"
  domain: "http://ucloud.saas-base-dd.suanshubang.cc"
  timeout: 3s
  connectTimeout: 1s
  retry: 1
  httpStat: false
  host: ""
  proxy: ""
  maxIdleConns: 20
  idleConnTimeout: 300s

qdmis:
  service: "qdmis"
  domain: "http://qdmis.toufang-wxf1-dd.suanshubang.cc/qdmis"
  timeout: 3s
  connectTimeout: 1s
  retry: 1
  httpStat: false
  host: ""
  proxy: ""
  maxIdleConns: 20
  idleConnTimeout: 300s

wxqkgo:
  service: wxqkgo
  domain: http://wxtools-rlink-cc.suanshubang.cc
  #domain: http://wxqk-go-svc.laxin-qk:8080
  timeout: 3s
  retry: 1

moat:
  service: moat
  domain: http://c3-sell-gateway-base-cc.suanshubang.cc
#  domain: http://moat2-svc.sell-gateway:8080
  timeout: 2s
  retry: 1

alps:
  service: alps
  #domain: https://igway.zuoyebang.cc/alps
  domain: http://igway-saasinfo-cc.suanshubang.cc/alps
  timeout: 1500ms
  retry: 1

dyzt:
  service: kidtutor
  domain: http://lpservice-svc.kid:8080
  timeout: 1300ms
  retry: 1

goqdmis:
  service: goqdmis
  domain: http://qdmis-base-cc.suanshubang.cc
  timeout: 3s
  retry: 1

#进校服务
markapi:
  # 调用下游的服务名称
  service: markapi
  # 请求完整地址
  #domain: https://zujuan.yunsizhixue.com
  domain: http://zhkt-work996-cc.suanshubang.cc #markapi-svc.mark:8080
  # 超时配置，time.Duration 类型
  timeout: 3000ms
  # 到该服务的最大空闲连接数
  maxIdleConns: 100
  # 空闲连接超时关闭时间
  idleConnTimeout: 300s
  maxReqBodyLen: 0
  # 重试次数，最多执行retry+1次
  retry: 2

#gotoufang
gotoufang:
  # 调用下游的服务名称
  service: gotoufang
  # 请求完整地址
  domain: http://apigo-tf-work996-e.suanshubang.cc
  #domain: http://go-toufang-svc.toufang:8080
  # 超时配置，time.Duration 类型
  timeout: 3000ms
  # 到该服务的最大空闲连接数
  maxIdleConns: 100
  # 空闲连接超时关闭时间
  idleConnTimeout: 300s
  maxReqBodyLen: 0
  # 重试次数，最多执行retry+1次
  retry: 2

gtmis:
  # 调用下游的服务名称
  service: gtmis
  # 请求完整地址
  domain: http://gtms-hetuoptimize-cc.suanshubang.cc
  #domain: http://gtms.zuoyebang.cc
  # 超时配置，time.Duration 类型
  timeout: 3000ms
  # 到该服务的最大空闲连接数
  maxIdleConns: 100
  # 空闲连接超时关闭时间
  idleConnTimeout: 300s
  maxReqBodyLen: 0
  # 重试次数，最多执行retry+1次
  retry: 2

assistantdeskgo:
  service: assistantdeskgo
  domain: http://assistantdesk-wxf1-cc.suanshubang.cc
  retry: 2
  timeout: 3s