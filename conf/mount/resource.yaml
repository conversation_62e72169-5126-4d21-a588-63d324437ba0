mysql:
  fenxiao:
    service: fenxiao
    database: homework_zhibo_fenxiao
    addr: 10.117.136.83:3306
    user: homework
    password: homework
    maxidleconns: 50
    maxopenconns: 100
    maxIdleTime: 300s
    connMaxLifeTime: 3600s
    connTimeOut: 1500ms
    writeTimeOut: 3s
    readTimeOut: 3s
  qudao:
    service: qudao
    database: homework_zhibo_qudao
    addr: 10.117.136.83:3306
    user: homework
    password: homework
    maxidleconns: 10
    maxopenconns: 1000
    connMaxLifeTime: 3600
    connTimeOut: 1500ms
    writeTimeOut: 1500ms
    readTimeOut: 1500ms
  doris:
    database: sr_data
    addr: 10.117.136.83:3306
    user: homework
    password: homework
    maxidleconns: 10
    maxopenconns: 1000
    connMaxLifeTime: 3600
    connTimeOut: 1500ms
    writeTimeOut: 1500ms
    readTimeOut: 1500ms

redis:
  toufang:
    addr: redis.basic.suanshubang.cc:6379 #codis.basic.suanshubang.com:8700
    
cos:
  toufang:
    bucket: zyb-toufang
    cloud: tencent
    app_id: 1253445850
    secret_id: AKIDZz8LGxzfePt64oyYXEivvf9OQBeqoMhr
    secret_key: 3q89tMZXGkyvd6RownIzTnsgFYkUdWxG
    region: ap-beijing
    filesize_limit: 104857600
    directory: ""
    file_prefix: tf_
    is_public: 1
    thumbnail: 1


#golib 2.7.x 仅支持zos，不支持cos
zos:
  buckets:
    - bucket: zyb-toufang
    - directory: ""     #新项目 不建议使用
    - file_prefix: tf_  #新项目 强烈不建议使用
  client:
    connectTimeout: 100ms
    timeout: 30000ms
    retry: 1

rmq:
  producer:
    - service: gohetu
      nameserver: ship-rocketmq-work996-svc.mq:9876
      # 需要生产信息的topic名称
      topic: toufang_go-hetu_common
      retry: 3
      timeout: 1000ms
  consumer:
    # service: consumer 名称，不同 consumer 间不可重复
    - service: gohetu_common
      nameserver: ship-rocketmq-work996-svc.mq:9876
      topic: toufang_go-hetu_common
      group: toufang_go-hetu_consumer
      tags:
        - "900001" # 机构关停配置，根据机构圈选活动
      broadcast: false
      orderly: true
      retry: 3
      throttle: 0
      pull_interval: 0s

    # qdmis 释放公海
    - service: goqdmis_release
      nameserver: ship-rocketmq-work996-svc.mq:9876 #需要代理到ship
      # 需要消费信息的topic名称
      topic: laxin-zb-laxin  #qdmis
      # 消费组名称, 不同服务间要保持名称唯一
      group: toufang_go-hetu_qdmis_release
      # 要消费消息的标签, 为空的话则会默认消费所有消息
      tags:
        - "500030" # 同步伏羲代理商机构释放状态
        - "500032" # 同步伏羲代理商机构名称
      # 是否是广播消费模式 广播消费模式下每个消费者实例会全量消费所有的消息, 而集群模式下每个消费者实例会竞争消费每条消息
      broadcast: false
      # 顺序消费 对消息的处理顺序有要求的业务需要设置为true, false代表使用并发消费模式
      orderly: false
      retry: 2
      # 消费限速参数, 10代表单pod消费速率为10qps. 消息是否被限速可以查看消费日志中新增的throttleCost参数, 大于0则代表消息被限速了
      throttle: 0
      # 消息拉取间隔, 默认0, 通常消费场景无需设置, 遇到在上游瞬时生产流量过大导致消费者短时间压力上升时可以考虑设置此参数. 需要注意的是设置此参数可能会出现消息延迟消费的情况
      pull_interval: 0s