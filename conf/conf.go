package conf

import (
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/redis"
	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/server/http"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"git.zuoyebang.cc/pkg/golib/v2/zos"
)

// 配置文件对应的全局变量
var (
	BasicConf TBasic
	API       TApi
	RConf     ResourceConf
	CConf     CustomConf
)

// 基础配置 对应config.yaml
type TBasic struct {
	Pprof  base.PprofConfig
	Log    zlog.LogConfig
	Server http.ServerConfig
}

// 对应 api.yaml
type TApi struct {
	Upms            base.ApiClient
	Ucloud          base.ApiClient
	Qdmis           base.ApiClient
	Goqdmis         base.ApiClient `yaml:"goqdmis"`
	Wxqk            base.ApiClient
	Wxtools         base.ApiClient `yaml:"wxtools"`
	Wxqkgo          base.ApiClient
	Moat            base.ApiClient `yaml:"moat"`
	Alps            base.ApiClient `yaml:"alps"`
	Dyzt            base.ApiClient `yaml:"dyzt"`
	Markapi         base.ApiClient `yaml:"markapi"`
	Gotoufang       base.ApiClient `yaml:"gotoufang"`
	Gtmis           base.ApiClient `yaml:"gtmis"`
	AssistantDeskGo base.ApiClient `yaml:"assistantdeskgo"`
}

// 对应 resource.yaml
type ResourceConf struct {
	Redis map[string]redis.RedisConf
	Mysql map[string]base.MysqlConf
	//Cos   map[string]cos.BucketConfig
	Zos zos.CustomerConfig `yaml:"zos"`
	Rmq rmq.RmqConfig
}

type CustomConf struct {
	Ips  ipsConf
	Upms upmsConf
}

type ipsConf struct {
	AppId     string `yaml:"appId"`
	AppSecret string `yaml:"appSecret"`
	Domain    string `yaml:"domain"`
	Host      string `yaml:"host"`
	Redirect  string `yaml:"redirect"`
}

type upmsConf struct {
	SystemId   int    `yaml:"systemId"`
	RoleItemId string `yaml:"roleItemId"`
	DataItemId string `yaml:"dataItemId"`
}

func InitConf() {
	// 加载通用基础配置（必须）
	env.LoadConf("config.yaml", env.SubConfMount, &BasicConf)

	// 加载api调用相关配置（optional）
	env.LoadConf("api.yaml", env.SubConfMount, &API)

	// 加载资源类配置（optional）
	env.LoadConf("resource.yaml", env.SubConfMount, &RConf)

	env.LoadConf("custom.yaml", env.SubConfMount, &CConf)
}
