package router

import (
	fenxiaoHttp "git.zuoyebang.cc/ad/go-fenxiao/controllers/http"
	fenxiaoAccount "git.zuoyebang.cc/ad/go-fenxiao/controllers/http/account"
	fenxiaoMiddleware "git.zuoyebang.cc/ad/go-fenxiao/middleware"
	fenxiaoServices "git.zuoyebang.cc/ad/go-fenxiao/services"
	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"go-hetu/conf"
	"go-hetu/controllers/command"
	"go-hetu/controllers/http/account"
	"go-hetu/controllers/http/act"
	"go-hetu/controllers/http/afx"
	"go-hetu/controllers/http/afxconf"
	"go-hetu/controllers/http/api"
	"go-hetu/controllers/http/apptask"
	"go-hetu/controllers/http/course"
	"go-hetu/controllers/http/finger"
	"go-hetu/controllers/http/jxmp"
	"go-hetu/controllers/http/tools"
	"go-hetu/controllers/http/unionorder"
	"go-hetu/controllers/http/user"
	"go-hetu/controllers/http/zeroact"
	"go-hetu/controllers/http/ztf"
	"go-hetu/helpers"
	"go-hetu/middleware"
	actservice "go-hetu/services/act"
	"sync"

	"github.com/gin-gonic/gin"
)

var (
	once          = sync.Once{}
	actService    actservice.Service
	actRepository actservice.Repository
)

func initService() {
	once.Do(func() {
		actRepository = actservice.NewRepository(dbcontext.New(helpers.MysqlClient))
		actService = actservice.NewService(actRepository)
	})
}

func Http(engine *gin.Engine) {
	initService()
	router := engine.Group("/go-hetu")

	//进校小程序 - B端活动管理
	jxmpGroup := router.Group("/jxmp")
	{
		//新增鉴权
		authJxmp := jxmpGroup.Group("", middleware.CheckIpsAndAuth)
		//同步进校侧试卷信息 工具，新建试卷执行一次即可，不需要鉴权
		jxmpGroup.GET("/paper/sync", jxmp.SyncPaper)
		authJxmp.POST("/paper/del", jxmp.DelPaper)             //删除试卷
		authJxmp.POST("/upload", jxmp.UploadFile)              //上传文件
		authJxmp.GET("/school/list", jxmp.GetSchoolList)       //学校列表
		authJxmp.GET("/paper/filter", jxmp.PaperFilter)        //试卷筛选
		authJxmp.GET("/paper/ifream", jxmp.Ifream)             //跳转的ifream地址
		authJxmp.POST("/activity/create", jxmp.CreateActivity) //创建活动
		authJxmp.GET("/activity/list", jxmp.ActivityList)      //活动列表 + 检索+ 分页
		authJxmp.POST("/activity/edit", jxmp.EditActivity)     //活动编辑
		authJxmp.POST("/activity/del", jxmp.DelActivity)       //活动删除`
	}

	// 金手指功能
	fingerGroup := router.Group("/finger", middleware.CheckIpsAndAuth)
	{
		fingerGroup.POST("/defaultedit", finger.DefaultEdit)    //默认值-配置接口
		fingerGroup.GET("/defaultdetail", finger.DefaultDetail) //默认值-查询接口

		fingerGroup.GET("/inst/options", finger.InstOptions) // 代理商机构-筛选项
		fingerGroup.GET("/inst/list", finger.InstList)       // 代理商机构-列表接口
		fingerGroup.POST("/inst/edit", finger.InstEdit)      // 代理商机构-修改接口

		fingerGroup.POST("school/add", finger.SchoolAdd)       // 学校特殊规则-新增接口
		fingerGroup.GET("/school/list", finger.SchoolList)     // 学校特殊规则-列表
		fingerGroup.POST("/school/edit", finger.SchoolEdit)    // 学校特殊规则-修改
		fingerGroup.POST("/school/delete", finger.SchoolDel)   // 学校特殊规则-删除
		fingerGroup.GET("/school/schlist", finger.SchInfoList) // 学校特殊规则-学校搜索接口

		// oplog
		fingerGroup.GET("/oplog", finger.OpLog)                          // 操作记录接口
		fingerGroup.GET("/script/initFingerInst", finger.InitFingerInst) // 将afxInst中的数据同步到schoolFinger中
	}

	oncejob := router.Group("/once")
	oncejob.GET("/dirty/del", command.DelDirtyData)

	zeroact.RegisterHandlers(router.Group("/zeromanage", middleware.CheckIpsAndAuth, middleware.GetDataAuth))

	act.RegisterHandlers(router.Group("/act", middleware.CheckIpsAndAuth, middleware.GetDataAuth), actService)

	// 一级代理商相关
	afx.RegisterHandlers(router.Group("/afx", middleware.CheckIpsAndAuth, middleware.GetDataAuth))

	// app活动列表
	apptask.RegisterHandlers(router.Group("/apptask", middleware.CheckIpsAndAuth, middleware.GetDataAuth))
	// 推广产品配置，用于设置 产品推广一级与学科、学部的对应关系
	afxconf.RegisterHandlers(router.Group("/afxconf", middleware.CheckIpsAndAuth))

	// 内网接口
	api.RegisterHandlers(router.Group("/api", fenxiaoMiddleware.InnerIpCheck))

	courseGroup := router.Group("/api/course", middleware.CheckIpsSession)
	{
		courseGroup.POST("/buy", course.BuyCourse)
		courseGroup.GET("/getinfo", course.GetCourseInfo)
	}

	userGroup := router.Group("/api/user", middleware.CheckIpsSession)
	{
		userGroup.POST("/getlist", user.GetUserInfoList)
		userGroup.POST("/get-user-course", user.GetUserCourse)
	}

	// https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=*********
	qdmisService := fenxiaoServices.NewQdmisService(&conf.API.Qdmis)
	ucloudService := fenxiaoServices.NewUcloudService(&conf.API.Ucloud, "", "")
	accountService := fenxiaoServices.NewAccountService(helpers.MysqlClient)
	accountGroup := router.Group("/account", middleware.CheckIpsSession)
	{
		accountGroup.GET("/registerlist", account.RegisterList(qdmisService))
		accountGroup.POST("/create", account.Create(ucloudService, qdmisService, accountService))
		accountGroup.GET("/option", fenxiaoAccount.Option)
		accountGroup.GET("/selector", account.Selector(qdmisService))
		accountGroup.GET("/list", account.List(ucloudService, qdmisService, accountService))
		accountGroup.POST("/update", account.Update(ucloudService, qdmisService, accountService))
	}
	router.GET("/operate/history", middleware.CheckIpsSession, fenxiaoHttp.History(helpers.MysqlClient, ucloudService))

	//0转正
	zero := router.Group("/inst/ztf", middleware.CheckIpsAndAuth, middleware.GetDataAuth)
	{
		zero.POST("/list", ztf.PageListInstZtf)
		zero.GET("/export", ztf.ExportInstZtfList)
		zero.POST("/channel_set", ztf.SetChannelCode)
		zero.POST("/channel_oplog", ztf.PageListChannelLog)
		zero.POST("/coef_oplog", ztf.PageListHealthCoefLog)
		zero.POST("/coef_list", ztf.PageListHealthCoef)
		zero.POST("/coef_set", ztf.HealthcoefSet)
		zero.POST("/period_list", ztf.PageListPeriod)
		zero.POST("/detail_list", ztf.PageListDetail)
		zero.GET("/detail_export", ztf.PageListDetailExport)

		zero.POST("/hide_list", ztf.HidePageList)
		zero.POST("/hide_add", ztf.HideAdd)
		zero.POST("/hide_del", ztf.HideDel)
		zero.GET("/team/list", ztf.GetTeamNameList)

	}

	//定时任务相关接口
	job := router.Group("/job", middleware.AuthJob)
	{
		//job.GET("/ztf/qkdata/fetch", ztf.JobZtfFetchQkData)
		//job.GET("/ztf/qkdata/fetch_v2", ztf.V2JobZtfFetchQkData)
		job.GET("/ztf/qkdata/updateactcache_v3", ztf.UpdateQkActCache)
		job.GET("/ztf/qkdata/fetch_v3", ztf.V3JobFetchQkActData)
		//job.GET("/ztf/qkdata/update", ztf.JobUpdateHistoryData)
		job.GET("/ztf/qkdata/clear", ztf.JobZtfClearQkData)
		job.GET("/ztf/channel/fetch", ztf.JobZtfFetchChannel)

		job.GET("/zeroact/downloadtask/exec", zeroact.JobDownloadTaskExec)
	}

	// 一次性脚本
	onejob := router.Group("/onejob", middleware.AuthJob)
	{
		onejob.GET("/inst/init/datafix", afx.InitInstDataFix)
		onejob.GET("/inst/insert/relation", afx.ScriptFixUserRelation)
		onejob.GET("/inst/flowtype/refresh", afx.RefreshFLowTypeData)

		onejob.GET("/order/fix/ordercadre", afx.ScriptFixOrderCadre)
		onejob.GET("/user/fix/cadre", afx.FixAfxUserCadre)
		onejob.GET("/userrelation/fix/FixAFXUserRelationData", afx.FixAFXUserRelationData)
		onejob.GET("/ztf/refreshHealthCoef", ztf.JobZtfRefreshHealthCoef)

		onejob.GET("/dealUserRelationNew", tools.UserRelationNew) // 刷新tblAFXUserRelationNew
	}

	// 订单管理-透传用户信息到工作台
	unionOrderGroup := router.Group("/unionorder", middleware.CheckIpsAndAuth)
	{
		unionOrderGroup.POST("/getuserdesc", unionorder.GetUserDesc) // 获取用户信息
	}
}
