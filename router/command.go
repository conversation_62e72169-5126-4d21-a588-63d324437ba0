package router

import (
	"go-hetu/controllers/command"
	"go-hetu/helpers"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

/*
		命令行使用方式(可通过执行 go run main.go -h  查看)：
		goweb application

		Usage:
	  		goweb [command]

		Available Commands:
	  		job1    This is a job to do xxx

Flags:

	  -h, --help   help for goweb

		为了方便，go run main.go 默认启动http服务。
		go run main.go command  启动一个任务，比如，go run main.go job1
*/
func Commands(rootCmd *cobra.Command, engine *gin.Engine) {
	var initPromote = &cobra.Command{
		Use:   "initPromote",
		Short: "init tblAFXPromoteRelation",
		Run: func(cmd *cobra.Command, args []string) {
			helpers.Job.RunSync(nil, func(ctx *gin.Context) error {
				return command.InitPromote(ctx)
			})
		},
	}
	rootCmd.AddCommand(initPromote)

	var initUserRelation = &cobra.Command{
		Use:   "initUserRelation",
		Short: "init tblAFXUserRelation",
		Run: func(cmd *cobra.Command, args []string) {
			helpers.Job.RunSync(nil, func(ctx *gin.Context) error {
				return command.InitUserRelation(ctx)
			})
		},
	}
	rootCmd.AddCommand(initUserRelation)

	var updateScodeInstId = &cobra.Command{
		Use:   "updateScodeInstId",
		Short: "update tblAFXSCode",
		Run: func(cmd *cobra.Command, args []string) {
			helpers.Job.RunSync(nil, func(ctx *gin.Context) error {
				return command.UpdateScodeInstId(ctx)
			})
		},
	}
	rootCmd.AddCommand(updateScodeInstId)

	var copyInstToNewApp = &cobra.Command{
		Use:   "copyInstToNewApp",
		Short: "update tblAFXSCode",
		Run: func(cmd *cobra.Command, args []string) {
			helpers.Job.RunSync(nil, func(ctx *gin.Context) error {
				return command.CopyInstToNewApp(ctx)
			})
		},
	}
	rootCmd.AddCommand(copyInstToNewApp)
}
