package router

import (
	"fmt"
	"go-hetu/conf"
	"go-hetu/controllers/mq"

	"git.zuoyebang.cc/pkg/golib/v2/rmq"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var rmqConsumers = map[string]func(*gin.Context, rmq.Message) error{
	"goqdmis_release": mq.RmqConsumerRelease,
	"gohetu_common":   mq.RmqConsumerInstActShut,
}

func MQ(engine *gin.Engine) {
	for _, consumeConf := range conf.RConf.Rmq.Consumer {
		service := consumeConf.Service
		if consumer, exists := rmqConsumers[service]; exists {
			// 初始化消费者
			if err := rmq.InitConsumer(consumeConf); err != nil {
				panic("register rmq[" + service + "] error: " + err.Error())
			}

			// rmq 消费回调 handler 注册
			// service 参数需要与 resource.yaml 中对应 consumer 配置的 service 字段对应
			err := rmq.StartConsumer(engine, service, consumer)
			if err != nil {
				panic(fmt.Sprintf("Start consumer error service:%s, service:%s: %s", consumeConf.NameServer, service, err.Error()))
			}
			zlog.InfoLogger(nil, "StartConsumer", zap.String("service", service))
		} else {
			zlog.ErrorLogger(nil, "Consumer not found", zap.String("service", service))
		}
	}
}
