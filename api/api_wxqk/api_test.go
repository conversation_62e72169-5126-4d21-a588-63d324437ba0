package api_wxqk

import (
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"testing"
)

var ctx = &gin.Context{}

func TestMain(m *testing.M) {
	env.SetRootPath("../..")
	helpers.PreInit()
	defer helpers.Clear()
	m.Run()
}

func TestGetChannelListByTeam(t *testing.T) {
	h5TaskID := 1611471
	pageNo := 1
	pageSize := 10
	channelID := 0
	resp, err := GetChannelListByTeam(ctx, h5TaskID, pageNo, pageSize, channelID)
	fmt.Printf("resp=[%+v], err=[%v]", resp, err)
}