package api_wxqk

const (
	ChannelStateValid   = 0
	ChannelStateInvalid = 1
)

const Cookie = "IPS_0b9d081e68c3862029f7521b0c4e36661744102934" // 为了在线下调用线上wxqk模块

type GetDataParam struct {
	ActIds     []int
	ChannelIds []int
	StartTime  int64
	EndTime    int64
	Pn         int
	Ps         int
}

type GetActResp struct {
	ActIdList []int `mapstructure:"activityIdList"`
}

type GetDataResp struct {
	Meta struct {
		Total int64
	} `mapstructure:"meta"`
	List []struct {
		ChannelId     int    `mapstructure:"channelId"`
		ActId         int    `mapstructure:"activityId"`
		LabelId       int64  `mapstructure:"labelId"`
		LabelName     string `mapstructure:"LabelName"`
		SerialNumber  string `mapstructure:"serialNumber"`
		ChannelUv     int    `mapstructure:"channelUv"`     //访问人数
		ClickTag      int    `mapstructure:"clickTag"`      //点击人数
		GainQrCode    int    `mapstructure:"gainQrCode"`    //获取二维码人数
		PressQrCode   int    `mapstructure:"pressQrCode"`   //扫码人数
		WxGroupEnters int    `mapstructure:"wxGroupEnters"` //例子数
	} `mapstructure:"list"`
}

type GetChannelResp struct {
	Meta struct {
		Total int64
	} `mapstructure:"meta"`
	DataList []struct {
		ChannelId   int    `mapstructure:"channelId"`
		ChannelName string `mapstructure:"channelName"`
	} `mapstructure:"dataList"`
}

type GetChannelListResp struct {
	List []ChannelListItem `json:"list"`
	Meta Page              `json:"meta"`
}

type ChannelListItem struct {
	ChannelID      int    `json:"channelId"`      // 渠道ID
	ChannelName    string `json:"channelName"`    // 渠道名称
	ChannelState   int    `json:"channelState"`   // 0 生效 1失效
	ChannelLongUrl string `json:"channelLongUrl"` // 长链接
}

type Page struct {
	PageNo   int `json:"pageNo"`
	PageSize int `json:"pageSize"`
	Total    int `json:"total"`
}

type ListChannelParam struct {
	H5TaskId     int
	ChannelIds   string
	ChannelName  string
	ChannelState int // -1表示全量 0：打开 1: 关闭
	PageNo       int `json:"pageNo"`
	PageSize     int `json:"pageSize"`
}
