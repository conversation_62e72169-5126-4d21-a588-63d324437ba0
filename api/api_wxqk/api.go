package api_wxqk

import (
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"go-hetu/api"
	"go-hetu/components"
	"go-hetu/conf"
	"net/http"
)

var client = &conf.API.Wxqk
var wxToolsClient = &conf.API.Wxtools

const (
	GetChannelListByTeamURL = "/wxqk/api/channellistbyteam"
	PathListChannelInfo     = "/wxqk/api/channelinfolist"
	PathActivityBaseInfo    = "/wxqk/activity/baseinfolist"
)

func GetActByChannel(ctx *gin.Context, channelId int) (*GetActResp, error) {
	opt := base.HttpRequestOptions{
		RequestBody: map[string]interface{}{
			"channelId": channelId,
		},
		Encode: base.EncodeJson,
	}
	res, err := client.HttpPost(ctx, "/wxqk/hetu/getactivitybychannelid", opt)
	if err != nil {
		return nil, components.ErrorAPI.Wrap(err)
	}
	if res.HttpCode != http.StatusOK {
		return nil, components.ErrorAPI.Sprintf("http status error")
	}

	var resp GetActResp
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return nil, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return nil, components.ErrorAPI.Sprintf(errno)
	}
	return &resp, nil
}

// GetAllActByTf 获取端外投放周期内的活动
func GetAllActByTf(ctx *gin.Context) (*GetActResp, error) {
	opt := base.HttpRequestOptions{
		RequestBody: map[string]interface{}{
			"pn":            1,
			"rn":            1000,
			"stopLaunchDay": 1,
		},
		Encode: base.EncodeJson,
	}
	res, err := client.HttpPost(ctx, "/wxqk/hetu/getactbystoplaunchday", opt)
	if err != nil {
		return nil, components.ErrorAPI.Wrap(err)
	}
	if res.HttpCode != http.StatusOK {
		return nil, components.ErrorAPI.Sprintf("http status error")
	}

	var resp GetActResp
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return nil, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return nil, components.ErrorAPI.Sprintf(errno)
	}
	return &resp, nil
}

func GetDataDetailByAct(ctx *gin.Context, param *GetDataParam) (*GetDataResp, error) {
	opt := base.HttpRequestOptions{
		RequestBody: map[string]interface{}{
			"pn":          param.Pn,
			"rn":          param.Ps,
			"startTime":   param.StartTime,
			"endTime":     param.EndTime,
			"activityIds": param.ActIds,
			"channelIds":  param.ChannelIds,
		},
		Encode: base.EncodeJson,
		//Cookies: map[string]string{ // 线下使用域名访问，需要加cookie
		//	"ZYBIPSCAS": Cookie,
		//},
	}
	res, err := client.HttpPost(ctx, "/wxqk/hetu/h5actdataforoutside", opt)
	if err != nil {
		return nil, components.ErrorAPI.Wrap(err)
	}
	if res.HttpCode != http.StatusOK {
		return nil, components.ErrorAPI.Sprintf("http status error")
	}

	var resp GetDataResp
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return nil, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return nil, components.ErrorAPI.Sprintf(errno)
	}
	return &resp, nil
}

func GetChannel(ctx *gin.Context, pn, ps, lastId int) (*GetChannelResp, error) {
	opt := base.HttpRequestOptions{
		RequestBody: map[string]interface{}{
			"pageNo":      pn,
			"pageSize":    ps,
			"startLastId": lastId,
		},
		Encode: base.EncodeJson,
	}
	res, err := client.HttpPost(ctx, "/wxqk/hetu/outsidelist", opt)
	if err != nil {
		return nil, components.ErrorAPI.Wrap(err)
	}
	if res.HttpCode != http.StatusOK {
		return nil, components.ErrorAPI.Sprintf("http status error")
	}

	var resp GetChannelResp
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return nil, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return nil, components.ErrorAPI.Sprintf(errno)
	}
	return &resp, nil
}

// GetChannelListByTeam Api - H5任务渠道列表 http://yapi.zuoyebang.cc/project/1723/interface/api/282435
func GetChannelListByTeam(ctx *gin.Context, h5TaskID int, pageNo int, pageSize int, channelID int) (resp GetChannelListResp, err error) {
	requestBody := map[string]interface{}{
		"h5TaskId": h5TaskID,
		"pageNo":   pageNo,
		"pageSize": pageSize,
	}
	if channelID > 0 {
		requestBody["channelId"] = channelID
	}
	opt := base.HttpRequestOptions{
		RequestBody: requestBody,
	}
	res, err := client.HttpGet(ctx, GetChannelListByTeamURL, opt)
	if err != nil {
		return resp, err
	}
	if res.HttpCode != http.StatusOK {
		return resp, components.ErrorAPI.Sprintf("http status error" + cast.ToString(res.HttpCode))
	}
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return resp, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return resp, components.ErrorAPI.Sprintf(errno)
	}
	return resp, nil
}

func ListChannelInfo(ctx *gin.Context, param ListChannelParam) (resp GetChannelListResp, err error) {
	req := map[string]interface{}{
		"h5TaskId":     param.H5TaskId,
		"pageNo":       param.PageNo,
		"pageSize":     param.PageSize,
		"channelState": param.ChannelState,
	}
	if param.ChannelIds != "" {
		req["channelIds"] = param.ChannelIds
	}
	if param.ChannelName != "" {
		req["channelName"] = param.ChannelName
	}

	opt := base.HttpRequestOptions{
		RequestBody: req,
	}
	res, err := client.HttpGet(ctx, PathListChannelInfo, opt)
	if err != nil {
		return resp, err
	}
	if res.HttpCode != http.StatusOK {
		return resp, components.ErrorAPI.Sprintf("http status error" + cast.ToString(res.HttpCode))
	}
	if errno, derr := api.DecodeResponse(ctx, res, &resp); derr != nil {
		return resp, components.ErrorAPI.Wrap(derr)
	} else if errno != 0 {
		return resp, components.ErrorAPI.Sprintf(errno)
	}
	return resp, nil
}

func ActivityBaseInfo(ctx *gin.Context, activityIds []int) (resJson gjson.Result, err error) {
	opt := base.HttpRequestOptions{
		//Cookies: map[string]string{ // 线下使用域名访问，需要加cookie
		//	"ZYBIPSCAS": Cookie,
		//},
	}

	if len(activityIds) > 0 {
		req := map[string]interface{}{
			"activityIds": activityIds,
		}
		opt.RequestBody = req
	}

	res, err := wxToolsClient.HttpGet(ctx, PathActivityBaseInfo, opt)
	if err != nil {
		return
	}
	resJson = gjson.ParseBytes(res.Response)
	if resJson.Get("errNo").Int() != 0 {
		err = errors.New(fmt.Sprintf("message: %s,code: %d", resJson.Get("errStr").String(), resJson.Get("code").Int()))
		zlog.Warnf(ctx, "[ActivityBaseInfo] errNo not 0, resp: %+v", resJson)
		return
	}
	return
}
