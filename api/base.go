package api

import (
	"encoding/json"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"net/http"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"
)

func DecodeResponse(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	var r base.DefaultRender
	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(res.Ctx, "http response decode err, err: %s", res.Response)
		return errno, err
	}

	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Errorf(res.Ctx, "http response code: %d", r.<PERSON>rrNo)
		return errno, errors.<PERSON><PERSON><PERSON>("%d-%s", errno, r.ErrMsg)
	}

	if _, ok := r.Data.(map[string]interface{}); !ok {
		return errno, nil
	}

	if err := mapstructure.Decode(r.Data, &output); err != nil {
		zlog.Warnf(ctx, "api call data decode error: %s", err.Error())
		return errno, err
	}

	return errno, nil
}

// 兼容数据格式为 {"errNo":0, "data":[]} 的形式
func DecodeResponseV2(ctx *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	r := struct {
		ErrNo  int             `json:"errNo"`
		ErrMsg string          `json:"errMsg"`
		Data   json.RawMessage `json:"data"`
	}{}

	if err = json.Unmarshal(res.Response, &r); err != nil {
		zlog.Errorf(res.Ctx, "http response decode err, err: %s", res.Response)
		return errno, err
	}

	errno = r.ErrNo
	if r.ErrNo != 0 {
		zlog.Warnf(res.Ctx, "http response code: %d", r.ErrNo)
		return errno, errors.Errorf("%d-%s", errno, r.ErrMsg)
	}

	if err := json.Unmarshal(r.Data, &output); err != nil {
		zlog.Warnf(ctx, "api call data decode error: %s", err.Error())
		return errno, err
	}

	return errno, nil
}

const (
	defaultErrNo = -1 //默认errno
)

// copy from func DecodeResponse(), 部分api接口返回值是code/msg/data
func DecodeResponseAlps(_ *gin.Context, res *base.ApiResult, output interface{}) (errno int, err error) {
	if res.HttpCode != http.StatusOK {
		return defaultErrNo, fmt.Errorf("invalid http response status code: %d", res.HttpCode)
	}
	var r struct {
		Code int             `json:"code"`
		Msg  string          `json:"msg"`
		Data json.RawMessage `json:"data"`
	}
	// 先解析数据整体
	if err = json.Unmarshal(res.Response, &r); err != nil {
		return defaultErrNo, fmt.Errorf("http response json decode err, err: %s, response: %s", err, utils.BytesToString(res.Response))
	}
	// 检查错误，非0返回错误
	errno = r.Code
	if r.Code != 0 {
		_ = json.Unmarshal(r.Data, &output)
		return errno, fmt.Errorf("errNo=%d, errMsg=%s", r.Code, r.Msg)
	}

	// 解析Data
	if err = json.Unmarshal(r.Data, &output); err != nil {
		return defaultErrNo, fmt.Errorf("api call data decode error: %s", err)
	}
	return errno, nil
}
