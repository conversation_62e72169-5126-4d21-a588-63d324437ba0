package act

import (
	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"github.com/gin-gonic/gin"
	"go-hetu/services/act/data"
	"gorm.io/gorm/clause"
)

type Repository interface {
	GetDB(ctx *gin.Context) *dbcontext.DB
	GetInstList(ctx *gin.Context, params data.ListInstReq) (resp data.ListInstResp, err error)
	BatchInsertInstConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig) (err error)
	BatchUpsertInstConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig) (err error)
	BatchGetInstConfigsByInstIds(ctx *gin.Context, instIDs []int64) (configs []tfmodels.AFXInstToggleConfig, err error)
	BatchInsertInstActConfig(ctx *gin.Context, instActConfigs []tfmodels.AFXInstActToggleConfig) (err error)
	BatchUpsertInstActConfig(ctx *gin.Context, instActConfigs []tfmodels.AFXInstActToggleConfig) (err error)
	UpdateInstByInstIds(ctx *gin.Context, ids []int64, data map[string]interface{}) (err error)
	UpdateInstActByInstIds(ctx *gin.Context, instIds []int64, data map[string]interface{}) (err error)
	GetActListByIds(ctx *gin.Context, ids []int64) (list []tfmodels.AFXInstActToggleConfig, err error)
	GetActList(ctx *gin.Context, params data.ActListReq) (resp data.ActListResp, err error)
	UpdateActByInstActId(ctx *gin.Context, instID int64, actID int64, data map[string]interface{}) (err error)
	BatchAddAct(ctx *gin.Context, insertData []tfmodels.AFXInstActToggleConfig) (err error)
	BatchInsertOperatorLog(ctx *gin.Context, logData []tfmodels.AFXInstActToggleOperationLog) (err error)
	GetOperatorList(ctx *gin.Context, params data.OperatorListReq) (resp data.OperatorListResp, err error)
	UpdateActByInstActIds(ctx *gin.Context, ids []int64, data map[string]interface{}) (err error)
	BatchGetInstActConfigByInstIds(ctx *gin.Context, actID int64, instIds []int) (list []tfmodels.AFXInstActToggleConfig, err error)
}

type repository struct {
	db *dbcontext.DB
}

func NewRepository(db *dbcontext.DB) Repository {
	return repository{db: db}
}

func (r repository) BatchGetInstActConfigByInstIds(ctx *gin.Context, actID int64, instIds []int) (list []tfmodels.AFXInstActToggleConfig, err error) {
	list = make([]tfmodels.AFXInstActToggleConfig, 0)
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).Scopes(tfmodels.WithNotDeleted).
		Where("act_id = ?", actID).
		Where("inst_id in (?)", instIds).
		Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repository) GetActListByIds(ctx *gin.Context, ids []int64) (list []tfmodels.AFXInstActToggleConfig, err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).Scopes(tfmodels.WithNotDeleted).
		Where("id in (?)", ids).Find(&list).Error; err != nil {
		return nil, err
	}
	return list, nil
}

func (r repository) UpdateActByInstActIds(ctx *gin.Context, ids []int64, data map[string]interface{}) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).Scopes(tfmodels.WithNotDeleted).
		Where("id in ?", ids).
		Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) GetOperatorList(ctx *gin.Context, params data.OperatorListReq) (resp data.OperatorListResp, err error) {
	resp = data.OperatorListResp{
		Total: 0,
		List:  make([]tfmodels.AFXInstActToggleOperationLog, 0),
	}
	dbc := r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleOperationLog).Scopes(tfmodels.WithNotDeleted)
	if params.InstID > 0 {
		dbc = dbc.Where("inst_id = ?", params.InstID)
	}
	if params.ActID > 0 {
		dbc = dbc.Where("act_id = ?", params.ActID)
	}
	if err = dbc.Count(&resp.Total).Error; err != nil {
		return resp, err
	}
	if params.PageNo > 0 {
		dbc = dbc.Limit(params.PageSize).Offset((params.PageNo - 1) * params.PageSize)
	}
	dbc.Order("`id` desc")
	if err = dbc.Find(&resp.List).Error; err != nil {
		return resp, err
	}
	return resp, err
}

func (r repository) BatchInsertOperatorLog(ctx *gin.Context, logData []tfmodels.AFXInstActToggleOperationLog) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleOperationLog).
		CreateInBatches(logData, 50).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) BatchAddAct(ctx *gin.Context, insertData []tfmodels.AFXInstActToggleConfig) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
		CreateInBatches(insertData, 50).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) UpdateActByInstActId(ctx *gin.Context, instID int64, actID int64, data map[string]interface{}) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).Scopes(tfmodels.WithNotDeleted).
		Where("inst_id = ? AND act_id = ?", instID, actID).
		Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) GetActList(ctx *gin.Context, params data.ActListReq) (resp data.ActListResp, err error) {
	resp = data.ActListResp{
		Total: 0,
		List:  make([]tfmodels.AFXInstActToggleConfig, 0),
	}
	dbc := r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
		Scopes(tfmodels.WithNotDeleted)
	if params.InstID > 0 {
		dbc = dbc.Where("inst_id = ?", params.InstID)
	}
	if params.Operator != "" {
		dbc = dbc.Where("operator like ?", "%"+params.Operator+"%")
	}
	if params.StartTime > 0 && params.EndTime > params.StartTime {
		dbc = dbc.Where("create_time >= ? and create_time <= ?", params.StartTime, params.EndTime)
	}
	if err = dbc.Count(&resp.Total).Error; err != nil {
		return resp, err
	}
	if params.PageNo > 0 {
		dbc = dbc.Limit(params.PageSize).Offset((params.PageNo - 1) * params.PageSize)
	}
	dbc.Order("`id` desc")
	if err = dbc.Find(&resp.List).Error; err != nil {
		return resp, err
	}
	return resp, err
}

func (r repository) UpdateInstByInstIds(ctx *gin.Context, instIds []int64, data map[string]interface{}) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).
		Scopes(tfmodels.WithNotDeleted).
		Where("inst_id in ?", instIds).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) UpdateInstActByInstIds(ctx *gin.Context, instIds []int64, data map[string]interface{}) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
		Scopes(tfmodels.WithNotDeleted).
		Where("inst_id in ?", instIds).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) BatchInsertInstActConfig(ctx *gin.Context, instActConfigs []tfmodels.AFXInstActToggleConfig) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
		CreateInBatches(instActConfigs, 50).Error; err != nil {
		return err
	}
	return nil
}

// BatchUpsertInstActConfig 批量插入或更新机构活动配置（使用 ON DUPLICATE KEY UPDATE）
func (r repository) BatchUpsertInstActConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstActToggleConfig) (err error) {
	if len(instConfigs) == 0 {
		return nil
	}

	// 使用 GORM 的 Clauses 来实现 ON DUPLICATE KEY UPDATE
	// 这里假设表有唯一索引 (inst_id)
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstActToggleConfig).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "inst_id"}, {Name: "act_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"status", "operator", "is_new", "update_time"}),
		}).CreateInBatches(instConfigs, 50).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) BatchInsertInstConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig) (err error) {
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).
		CreateInBatches(instConfigs, 50).Error; err != nil {
		return err
	}
	return nil
}

func (r repository) GetInstList(ctx *gin.Context, params data.ListInstReq) (resp data.ListInstResp, err error) {
	resp = data.ListInstResp{
		Total: 0,
		List:  make([]tfmodels.AFXInstToggleConfig, 0),
	}
	dbc := r.db.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).Scopes(tfmodels.WithNotDeleted)
	if params.AppID > 0 {
		dbc = dbc.Where("app_id = ?", params.AppID)
	}
	if len(params.InstIDs) > 0 {
		dbc = dbc.Where("inst_id in (?)", params.InstIDs)
	}
	if params.Operator != "" {
		dbc = dbc.Where("operator like ?", "%"+params.Operator+"%")
	}
	if params.StartTime > 0 && params.EndTime > params.StartTime {
		dbc = dbc.Where("create_time >= ? and create_time <= ?", params.StartTime, params.EndTime)
	}
	if err = dbc.Count(&resp.Total).Error; err != nil {
		return resp, err
	}
	if params.PageNo > 0 {
		dbc = dbc.Limit(params.PageSize).Offset((params.PageNo - 1) * params.PageSize)
	}
	dbc.Order("`id` desc")
	if err = dbc.Find(&resp.List).Error; err != nil {
		return resp, err
	}
	return resp, err
}

func (r repository) GetDB(ctx *gin.Context) *dbcontext.DB {
	return r.db
}

// BatchGetInstConfigsByInstIds 查询已存在的机构配置
func (r repository) BatchGetInstConfigsByInstIds(ctx *gin.Context, instIDs []int64) (configs []tfmodels.AFXInstToggleConfig, err error) {
	configs = make([]tfmodels.AFXInstToggleConfig, 0)
	if len(instIDs) == 0 {
		return configs, nil
	}

	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).
		Scopes(tfmodels.WithNotDeleted).
		Where("inst_id IN ?", instIDs).
		Find(&configs).Error; err != nil {
		return nil, err
	}
	return configs, nil
}

// BatchUpsertInstConfig 批量插入或更新机构配置（使用 ON DUPLICATE KEY UPDATE）
func (r repository) BatchUpsertInstConfig(ctx *gin.Context, instConfigs []tfmodels.AFXInstToggleConfig) (err error) {
	if len(instConfigs) == 0 {
		return nil
	}

	// 使用 GORM 的 Clauses 来实现 ON DUPLICATE KEY UPDATE
	// 这里假设表有唯一索引 (inst_id)
	if err = r.db.WithContext(ctx).Table(tfmodels.TblAFXInstToggleConfig).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "inst_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"app_id", "status", "operator", "update_time"}),
		}).CreateInBatches(instConfigs, 50).Error; err != nil {
		return err
	}
	return nil
}
