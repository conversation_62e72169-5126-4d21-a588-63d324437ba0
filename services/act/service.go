package act

import (
	"fmt"
	"git.zuoyebang.cc/ad/gocommons/const/fenxiaoconsts"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"go-hetu/middleware"
	"go-hetu/services/act/data"
	"go-hetu/utils"
	"time"
)

type Service interface {
	InstList(ctx *gin.Context, params data.ListInstReq) (resp data.ListInstResp, err error)
	BatchAddInst(ctx *gin.Context, params data.BatchAddInstReq) (err error)
	BatchUpsertInst(ctx *gin.Context, params data.BatchAddInstReq) (err error)
	BatchUpdateInst(ctx *gin.Context, params data.BatchUpdateInstReq) (err error)
	ActList(ctx *gin.Context, params data.ActListReq) (resp data.ActListResp, err error)
	UpdateAct(ctx *gin.Context, params data.UpdateActReq) (err error)
	BatchAddAct(ctx *gin.Context, params data.BatchAddActReq) (err error)
	BatchUpsertAct(ctx *gin.Context, params data.BatchAddActReq) (err error)
	OperatorList(ctx *gin.Context, params data.OperatorListReq) (resp data.OperatorListResp, err error)
	BatchUpdateAct(ctx *gin.Context, params data.BatchUpdateActReq) (err error)
	FlushHistoryInstActConfig(ctx *gin.Context, params data.FlushHistoryInstActConfigReq) (err error)
}

func NewService(repo Repository) Service {
	return service{
		repo: repo,
	}
}

type service struct {
	repo Repository
}

func (s service) BatchUpdateAct(ctx *gin.Context, params data.BatchUpdateActReq) (err error) {
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) (err error) {
		err = s.repo.UpdateActByInstActIds(ctx, params.InstActIds, map[string]interface{}{
			"status":      params.OperatorType,
			"update_time": time.Now().Unix(),
		})
		if err != nil {
			return err
		}

		instActList, err := s.repo.GetActListByIds(ctx, params.InstActIds)
		if err != nil {
			return err
		}

		var (
			logData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
		)
		for _, item := range instActList {
			logData = append(logData, tfmodels.AFXInstActToggleOperationLog{
				InstID:       item.InstID,
				ActID:        item.ActID,
				Operator:     middleware.GetUserInfoFromCtx(ctx).Uname,
				OperatorType: params.OperatorType,
			})
		}
		err = s.repo.BatchInsertOperatorLog(ctx, logData)
		if err != nil {
			return err
		}
		return nil
	})
}

func (s service) OperatorList(ctx *gin.Context, params data.OperatorListReq) (resp data.OperatorListResp, err error) {
	return s.repo.GetOperatorList(ctx, params)
}

// BatchUpsertAct 批量插入或更新机构配置（支持更新逻辑）
func (s service) BatchUpsertAct(ctx *gin.Context, params data.BatchAddActReq) (err error) {
	if len(params.InstIDs) == 0 {
		return nil
	}

	var (
		upsertData      = make([]tfmodels.AFXInstActToggleConfig, 0)
		operatorLogData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
		currentTime     = time.Now().Unix()
		operator        = middleware.GetUserInfoFromCtx(ctx).Uname
	)

	// 为每个机构ID准备数据
	for _, instID := range params.InstIDs {
		// 如果已存在，准备更新数据
		upsertData = append(upsertData, tfmodels.AFXInstActToggleConfig{
			ActID:      params.ActID,
			InstID:     instID,
			Status:     params.OperatorType,
			Operator:   operator,
			CreateTime: currentTime, // 保留原有创建时间
			UpdateTime: currentTime, // 更新修改时间
		})

		// 准备操作日志
		operatorLogData = append(operatorLogData, tfmodels.AFXInstActToggleOperationLog{
			InstID:       instID,
			ActID:        params.ActID,
			Operator:     operator,
			OperatorType: params.OperatorType,
			CreateTime:   currentTime,
			UpdateTime:   currentTime,
		})
	}

	// 在事务中执行批量upsert操作
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) error {
		// 执行批量upsert
		err = s.repo.BatchUpsertInstActConfig(ctx, upsertData)
		if err != nil {
			return err
		}

		// 添加操作日志
		err = s.repo.BatchInsertOperatorLog(ctx, operatorLogData)
		if err != nil {
			return err
		}
		return nil
	})
}

func (s service) BatchAddAct(ctx *gin.Context, params data.BatchAddActReq) (err error) {
	var (
		insertData      = make([]tfmodels.AFXInstActToggleConfig, 0)
		operatorLogData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
	)
	for _, instId := range params.InstIDs {
		insertData = append(insertData, tfmodels.AFXInstActToggleConfig{
			InstID:   instId,
			ActID:    params.ActID,
			Status:   params.OperatorType,
			Operator: middleware.GetUserInfoFromCtx(ctx).Uname,
		})
		operatorLogData = append(operatorLogData, tfmodels.AFXInstActToggleOperationLog{
			InstID:       instId,
			ActID:        params.ActID,
			Operator:     middleware.GetUserInfoFromCtx(ctx).Uname,
			OperatorType: params.OperatorType,
		})
	}
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) error {
		err = s.repo.BatchAddAct(ctx, insertData)
		if err != nil {
			return err
		}
		err = s.repo.BatchInsertOperatorLog(ctx, operatorLogData)
		if err != nil {
			return err
		}
		return nil
	})
}

func (s service) UpdateAct(ctx *gin.Context, params data.UpdateActReq) (err error) {
	return s.repo.UpdateActByInstActId(ctx, params.InstID, params.ActID, map[string]interface{}{
		"status":      params.OperatorType,
		"update_time": time.Now().Unix(),
	})
}

func (s service) ActList(ctx *gin.Context, params data.ActListReq) (resp data.ActListResp, err error) {
	return s.repo.GetActList(ctx, params)
}

func (s service) BatchUpdateInst(ctx *gin.Context, params data.BatchUpdateInstReq) (err error) {
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) error {
		err = s.repo.UpdateInstByInstIds(ctx, params.InstIDs, map[string]interface{}{
			"status": params.OperatorType,
		})
		if err != nil {
			return err
		}
		err = s.repo.UpdateInstActByInstIds(ctx, params.InstIDs, map[string]interface{}{
			"status": params.OperatorType,
		})
		if err != nil {
			return err
		}
		var (
			logData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
		)
		for _, instID := range params.InstIDs {
			logData = append(logData, tfmodels.AFXInstActToggleOperationLog{
				InstID:       instID,
				ActID:        0,
				Operator:     middleware.GetUserInfoFromCtx(ctx).Uname,
				OperatorType: params.OperatorType,
			})
		}
		err = s.repo.BatchInsertOperatorLog(ctx, logData)
		if err != nil {
			return err
		}
		return nil
	})
}

func (s service) BatchAddInst(ctx *gin.Context, params data.BatchAddInstReq) (err error) {
	var (
		insertData      = make([]tfmodels.AFXInstToggleConfig, 0)
		operatorLogData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
	)
	for _, instID := range params.InstIDs {
		insertData = append(insertData, tfmodels.AFXInstToggleConfig{
			AppID:    params.AppID,
			InstID:   instID,
			Status:   params.OperatorType,
			Operator: middleware.GetUserInfoFromCtx(ctx).Uname,
		})
		operatorLogData = append(operatorLogData, tfmodels.AFXInstActToggleOperationLog{
			InstID:       instID,
			ActID:        0,
			Operator:     middleware.GetUserInfoFromCtx(ctx).Uname,
			OperatorType: params.OperatorType,
		})
	}
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) error {
		err = s.repo.BatchInsertInstConfig(ctx, insertData)
		if err != nil {
			return err
		}
		// 添加日志
		err = s.repo.BatchInsertOperatorLog(ctx, operatorLogData)
		if err != nil {
			return err
		}
		// 发送mq，添加机构下的活动
		err = helpers.SendMsg(ctx, "900001", map[string]interface{}{
			"instIds":      params.InstIDs,
			"operatorType": params.OperatorType,
		})
		if err != nil {
			return err
		}
		return nil
	})
}

// BatchUpsertInst 批量插入或更新机构配置（支持更新逻辑）
func (s service) BatchUpsertInst(ctx *gin.Context, params data.BatchAddInstReq) (err error) {
	if len(params.InstIDs) == 0 {
		return nil
	}

	var (
		upsertData      = make([]tfmodels.AFXInstToggleConfig, 0)
		operatorLogData = make([]tfmodels.AFXInstActToggleOperationLog, 0)
		currentTime     = time.Now().Unix()
		operator        = middleware.GetUserInfoFromCtx(ctx).Uname
	)

	// 为每个机构ID准备数据
	for _, instID := range params.InstIDs {
		// 如果已存在，准备更新数据
		upsertData = append(upsertData, tfmodels.AFXInstToggleConfig{
			AppID:      params.AppID,
			InstID:     instID,
			Status:     params.OperatorType,
			Operator:   operator,
			CreateTime: currentTime, // 保留原有创建时间
			UpdateTime: currentTime, // 更新修改时间
		})

		// 准备操作日志
		operatorLogData = append(operatorLogData, tfmodels.AFXInstActToggleOperationLog{
			InstID:       instID,
			ActID:        0,
			Operator:     operator,
			OperatorType: params.OperatorType,
			CreateTime:   currentTime,
			UpdateTime:   currentTime,
		})
	}

	// 在事务中执行批量upsert操作
	return s.repo.GetDB(ctx).Transactional(ctx, func(ctx *gin.Context) error {
		// 执行批量upsert
		err = s.repo.BatchUpsertInstConfig(ctx, upsertData)
		if err != nil {
			return err
		}

		// 添加操作日志
		err = s.repo.BatchInsertOperatorLog(ctx, operatorLogData)
		if err != nil {
			return err
		}

		// 发送MQ消息，通知机构配置变更
		err = helpers.SendMsg(ctx, "900001", map[string]interface{}{
			"instIds":      params.InstIDs,
			"operatorType": params.OperatorType,
		})
		if err != nil {
			return err
		}
		return nil
	})
}

func (s service) InstList(ctx *gin.Context, params data.ListInstReq) (resp data.ListInstResp, err error) {
	return s.repo.GetInstList(ctx, params)
}

// FlushHistoryInstActConfig 刷历史机构活动配置数据
func (s service) FlushHistoryInstActConfig(ctx *gin.Context, params data.FlushHistoryInstActConfigReq) (err error) {
	var (
		totalCount int64 = 0
	)
	if err = helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAFXActInstToggle).
		Count(&totalCount).Error; err != nil {
		zlog.Errorf(ctx, "获取需要更新的记录总数失败: %v", err)
		return
	}

	// 如果没有需要更新的记录，直接返回
	if totalCount == 0 {
		return nil
	}

	zlog.Infof(ctx, "总共需要处理 %d 条记录", totalCount)

	var (
		pageNo      = 1
		pageSize    = 200 // 每批查询的记录数
		actExistMap = make(map[int64]struct{})
	)

	for pageNo = 1; ; pageNo++ {
		actInstToggles := make([]tfmodels.AFXActInstToggle, 0)
		query := helpers.MysqlClient.WithContext(ctx).Table(tfmodels.TblAFXActInstToggle)

		err = query.Order("id desc").Limit(pageSize).Offset((pageNo - 1) * pageSize).Find(&actInstToggles).Error
		if err != nil {
			return err
		}

		// 如果没有记录，说明已经处理完所有数据
		if len(actInstToggles) == 0 {
			break
		}

		waitProcessActInstRecords := make([]tfmodels.AFXActInstToggle, 0)
		for _, item := range actInstToggles {
			if _, ok := actExistMap[item.ActID]; ok {
				continue
			}
			actExistMap[item.ActID] = struct{}{}
			waitProcessActInstRecords = append(waitProcessActInstRecords, item)
		}

		if err = s.processActInstToggleBatch(ctx, waitProcessActInstRecords); err != nil {
			return
		}
	}
	return nil
}

func (s service) processActInstToggleBatch(ctx *gin.Context, records []tfmodels.AFXActInstToggle) error {
	for _, record := range records {
		// 当前活动关停的机构列表, 只需要刷入关停的活动即可，开启的无需处理
		shutInstIds := utils.SplitToIntSlice(record.CurrentInstIDs, ",")
		instActList, err := s.repo.BatchGetInstActConfigByInstIds(ctx, record.ActID, shutInstIds)
		if err != nil {
			return err
		}
		var (
			existInstActMap = map[string]tfmodels.AFXInstActToggleConfig{}
			insertData      = make([]tfmodels.AFXInstActToggleConfig, 0)
			updateData      = make([]tfmodels.AFXInstActToggleConfig, 0)
		)
		for _, instAct := range instActList {
			existInstActMap[fmt.Sprintf("%d_%d", instAct.InstID, instAct.ActID)] = instAct
		}
		for _, instId := range shutInstIds {
			if instAct, ok := existInstActMap[fmt.Sprintf("%d_%d", instId, record.ActID)]; ok {
				// 以更新时间最新的为准
				if record.UpdateTime > instAct.UpdateTime {
					updateData = append(updateData, instAct)
				}
				continue
			}
			insertData = append(insertData, tfmodels.AFXInstActToggleConfig{
				InstID:   int64(instId),
				ActID:    record.ActID,
				Status:   fenxiaoconsts.OperatorTypeClose,
				Operator: record.Operator,
			})
		}
		err = s.repo.BatchInsertInstActConfig(ctx, insertData)
		if err != nil {
			return err
		}
		for _, item := range updateData {
			err = s.repo.UpdateActByInstActId(ctx, item.InstID, item.ActID, map[string]interface{}{
				"status":      item.Status,
				"operator":    record.Operator,
				"update_time": time.Now().Unix(),
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}
