package data

import "git.zuoyebang.cc/ad/gocommons/tfmodels"

type (
	ListInstReq struct {
		AppID     int64   `json:"appId" form:"appId"` // 应用方ID
		InstIDs   []int64 `json:"instIds" form:"instIds"`
		Operator  string  `json:"operator" form:"operator"` // 操作人名称
		StartTime int64   `json:"startTime" form:"startTime"`
		EndTime   int64   `json:"endTime" form:"endTime"`
		PageNo    int     `json:"pageNo" form:"pageNo"`
		PageSize  int     `json:"pageSize" form:"pageSize"`
	}

	ListInstResp struct {
		Total int64                          `json:"total"`
		List  []tfmodels.AFXInstToggleConfig `json:"list"`
	}
)

type (
	BatchAddInstReq struct {
		AppID        int64   `json:"appId" form:"appId" binding:"required"`
		InstIDs      []int64 `json:"instIds" form:"instIds" binding:"required"`
		OperatorType int     `json:"operatorType" form:"operatorType" binding:"required"` // 1: 开启 2: 关闭
	}
	BatchAddInstResp struct {
	}
)

type (
	BatchUpdateInstReq struct {
		InstIDs      []int64 `json:"instIds" form:"instIds" binding:"required"`           // 配置id列表
		OperatorType int     `json:"operatorType" form:"operatorType" binding:"required"` // 1: 开启 2: 关闭
	}
	BatchUpdateInstResp struct {
	}
)

type (
	ActListReq struct {
		InstID    int64  `json:"instId" form:"instId"`
		Operator  string `json:"operator" form:"operator"`
		StartTime int64  `json:"startTime" form:"startTime"`
		EndTime   int64  `json:"endTime" form:"endTime"`
		PageNo    int    `json:"pageNo" form:"pageNo"`
		PageSize  int    `json:"pageSize" form:"pageSize"`
	}
	ActListResp struct {
		Total int64                             `json:"total"`
		List  []tfmodels.AFXInstActToggleConfig `json:"list"`
	}
)

type (
	UpdateActReq struct {
		InstID       int64 `json:"instId" form:"instId" binding:"required"`
		ActID        int64 `json:"actId" form:"actId" binding:"required"`
		OperatorType int   `json:"operatorType" form:"operatorType" binding:"required"` // 1: 开启 2: 关闭
	}

	UpdateActResp struct {
	}
)

type (
	BatchUpdateActReq struct {
		InstActIds   []int64 `json:"instActIds" form:"instActIds" binding:"required"`
		OperatorType int     `json:"operatorType" form:"operatorType" binding:"required"` // 1: 开启 2: 关闭
	}

	BatchUpdateActResp struct {
	}
)

type (
	BatchAddActReq struct {
		ActID        int64   `json:"actId" form:"actId" binding:"required"`
		InstIDs      []int64 `json:"instIds" form:"instIds" binding:"required"`
		OperatorType int     `json:"operatorType" form:"operatorType" binding:"required"` // 1: 开启 2: 关闭
	}
	BatchAddActResp struct {
	}
)

type (
	OperatorListReq struct {
		InstID       int64 `json:"instId" form:"instId" binding:"required"` // 机构id
		ActID        int64 `json:"actId" form:"actId"`
		OperatorType int   `json:"operatorType" form:"operatorType"` // 操作类型 1: 开启 2: 关闭
		PageNo       int   `json:"pageNo" form:"pageNo"`
		PageSize     int   `json:"pageSize" form:"pageSize"`
	}
	OperatorListResp struct {
		Total int64                                   `json:"total"`
		List  []tfmodels.AFXInstActToggleOperationLog `json:"list"`
	}
)

type (
	FlushHistoryInstActConfigReq struct {
	}

	FlushHistoryInstActConfigResp struct {
	}
)
