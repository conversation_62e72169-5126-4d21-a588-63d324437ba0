package act

import (
	"os"
	"testing"

	"git.zuoyebang.cc/ad/gocommons/dbcontext"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"go-hetu/middleware"
	"go-hetu/services/act/data"
)

var (
	ctx           *gin.Context
	actRepository Repository
	actService    Service
)

func TestMain(m *testing.M) {
	// 设置项目根路径
	env.SetRootPath("../..")

	// 初始化基础配置
	helpers.PreInit()

	// 初始化数据库
	helpers.InitMysql()

	// 创建测试上下文
	ctx = &gin.Context{}

	// 设置测试用户信息（用于批量操作测试）
	ctx.Set("IPS_UserInfo", &middleware.UserInfo{
		Uname: "test_user",
		Uid:   12345,
	})

	// 初始化服务
	actRepository = NewRepository(dbcontext.New(helpers.MysqlClient))
	actService = NewService(actRepository)

	// 运行测试
	os.Exit(m.Run())
}

func TestService_InstList(t *testing.T) {
	tests := []struct {
		name        string
		params      data.ListInstReq
		wantErr     bool
		checkResult bool
	}{
		{
			name: "正常查询-基本参数",
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含机构ID列表",
			params: data.ListInstReq{
				AppID:    1,
				InstIDs:  []int64{100, 200},
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含操作人筛选",
			params: data.ListInstReq{
				AppID:    1,
				Operator: "test",
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含时间范围",
			params: data.ListInstReq{
				AppID:     1,
				StartTime: 1640995200, // 2022-01-01
				EndTime:   1672531199, // 2022-12-31
				PageNo:    1,
				PageSize:  10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-最小分页",
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 1,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-大分页",
			params: data.ListInstReq{
				AppID:    1,
				PageNo:   1,
				PageSize: 100,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-无分页参数",
			params: data.ListInstReq{
				AppID: 1,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-AppID为0",
			params: data.ListInstReq{
				AppID:    0,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-空的机构ID列表",
			params: data.ListInstReq{
				AppID:    1,
				InstIDs:  []int64{},
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := actService.InstList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("InstList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.checkResult {
				// 检查返回结果的基本结构
				if resp.List == nil {
					t.Errorf("InstList() List should not be nil")
				}
				if resp.Total < 0 {
					t.Errorf("InstList() Total should not be negative, got %d", resp.Total)
				}
				// 如果有分页参数，检查返回的数据量
				if tt.params.PageSize > 0 && len(resp.List) > tt.params.PageSize {
					t.Errorf("InstList() returned more items than PageSize, expected <= %d, got %d", tt.params.PageSize, len(resp.List))
				}
			}
		})
	}
}

func TestService_BatchAddInst(t *testing.T) {
	tests := []struct {
		name    string
		params  data.BatchAddInstReq
		wantErr bool
	}{
		{
			name: "正常批量添加-开启状态",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{1001, 1002, 1003},
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常批量添加-关闭状态",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{1004, 1005},
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "单个机构添加",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{1006},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-空机构列表",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{},
				OperatorType: 1,
			},
			wantErr: false, // 空列表不会报错，但不会执行任何操作
		},
		{
			name: "边界测试-AppID为0",
			params: data.BatchAddInstReq{
				AppID:        0,
				InstIDs:      []int64{1007},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-无效操作类型",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{1008},
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false,
		},
		{
			name: "边界测试-负数机构ID",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{-1, -2},
				OperatorType: 1,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.BatchAddInst(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("BatchAddInst() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，检查是否正确处理了参数
			if !tt.wantErr {
				// 这里可以添加更多的验证逻辑，比如检查数据库中的记录
				// 但由于这是单元测试，我们主要关注方法是否能正常执行
				t.Logf("BatchAddInst() executed successfully for %d institutions", len(tt.params.InstIDs))
			}
		})
	}
}

func TestService_BatchUpdateInst(t *testing.T) {
	tests := []struct {
		name    string
		params  data.BatchUpdateInstReq
		wantErr bool
	}{
		{
			name: "正常批量更新-开启状态",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{100, 200, 300},
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常批量更新-关闭状态",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{400, 500},
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "单个机构更新",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{600},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-空机构列表",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{},
				OperatorType: 1,
			},
			wantErr: false, // 空列表不会报错，但不会更新任何记录
		},
		{
			name: "边界测试-负数机构ID",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{-1, -2},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-无效操作类型",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{700},
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false,
		},
		{
			name: "边界测试-操作类型为0",
			params: data.BatchUpdateInstReq{
				InstIDs:      []int64{800},
				OperatorType: 0,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.BatchUpdateInst(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateInst() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，记录成功信息
			if !tt.wantErr {
				t.Logf("BatchUpdateInst() executed successfully for %d institutions", len(tt.params.InstIDs))
			}
		})
	}
}

func TestService_ActList(t *testing.T) {
	tests := []struct {
		name        string
		params      data.ActListReq
		wantErr     bool
		checkResult bool
	}{
		{
			name: "正常查询-基本参数",
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含操作人筛选",
			params: data.ActListReq{
				InstID:   100,
				Operator: "admin",
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含时间范围",
			params: data.ActListReq{
				InstID:    100,
				StartTime: 1640995200, // 2022-01-01
				EndTime:   1672531199, // 2022-12-31
				PageNo:    1,
				PageSize:  10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-InstID为0",
			params: data.ActListReq{
				InstID:   0,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-无分页参数",
			params: data.ActListReq{
				InstID: 100,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-最小分页",
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 1,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-大分页",
			params: data.ActListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 100,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-无效时间范围",
			params: data.ActListReq{
				InstID:    100,
				StartTime: 1672531199, // 结束时间
				EndTime:   1640995200, // 开始时间（错误的顺序）
				PageNo:    1,
				PageSize:  10,
			},
			wantErr:     false,
			checkResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := actService.ActList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("ActList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.checkResult {
				// 检查返回结果的基本结构
				if resp.List == nil {
					t.Errorf("ActList() List should not be nil")
				}
				if resp.Total < 0 {
					t.Errorf("ActList() Total should not be negative, got %d", resp.Total)
				}
				// 如果有分页参数，检查返回的数据量
				if tt.params.PageSize > 0 && len(resp.List) > tt.params.PageSize {
					t.Errorf("ActList() returned more items than PageSize, expected <= %d, got %d", tt.params.PageSize, len(resp.List))
				}
			}
		})
	}
}

func TestService_UpdateAct(t *testing.T) {
	tests := []struct {
		name    string
		params  data.UpdateActReq
		wantErr bool
	}{
		{
			name: "正常更新-开启状态",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常更新-关闭状态",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "边界测试-InstID为0",
			params: data.UpdateActReq{
				InstID:       0,
				ActID:        200,
				OperatorType: 1,
			},
			wantErr: false, // 不会报错，但可能不会更新任何记录
		},
		{
			name: "边界测试-ActID为0",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        0,
				OperatorType: 1,
			},
			wantErr: false, // 不会报错，但可能不会更新任何记录
		},
		{
			name: "边界测试-负数InstID",
			params: data.UpdateActReq{
				InstID:       -1,
				ActID:        200,
				OperatorType: 1,
			},
			wantErr: false, // 不会报错，但可能不会更新任何记录
		},
		{
			name: "边界测试-负数ActID",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        -1,
				OperatorType: 1,
			},
			wantErr: false, // 不会报错，但可能不会更新任何记录
		},
		{
			name: "边界测试-无效操作类型",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false, // 不会报错，但业务逻辑可能不正确
		},
		{
			name: "边界测试-操作类型为0",
			params: data.UpdateActReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 0,
			},
			wantErr: false, // 不会报错，但业务逻辑可能不正确
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.UpdateAct(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateAct() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestService_BatchAddAct(t *testing.T) {
	tests := []struct {
		name    string
		params  data.BatchAddActReq
		wantErr bool
	}{
		{
			name: "正常批量添加活动-开启状态",
			params: data.BatchAddActReq{
				ActID:        1001,
				InstIDs:      []int64{100, 200, 300},
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常批量添加活动-关闭状态",
			params: data.BatchAddActReq{
				ActID:        1002,
				InstIDs:      []int64{400, 500},
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "单个机构添加活动",
			params: data.BatchAddActReq{
				ActID:        1003,
				InstIDs:      []int64{600},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-空机构列表",
			params: data.BatchAddActReq{
				ActID:        1004,
				InstIDs:      []int64{},
				OperatorType: 1,
			},
			wantErr: false, // 空列表不会报错，但不会执行任何操作
		},
		{
			name: "边界测试-ActID为0",
			params: data.BatchAddActReq{
				ActID:        0,
				InstIDs:      []int64{700},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-负数ActID",
			params: data.BatchAddActReq{
				ActID:        -1,
				InstIDs:      []int64{800},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-负数机构ID",
			params: data.BatchAddActReq{
				ActID:        1005,
				InstIDs:      []int64{-1, -2},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-无效操作类型",
			params: data.BatchAddActReq{
				ActID:        1006,
				InstIDs:      []int64{900},
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.BatchAddAct(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("BatchAddAct() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，记录成功信息
			if !tt.wantErr {
				t.Logf("BatchAddAct() executed successfully for ActID=%d with %d institutions", tt.params.ActID, len(tt.params.InstIDs))
			}
		})
	}
}

func TestService_OperatorList(t *testing.T) {
	tests := []struct {
		name        string
		params      data.OperatorListReq
		wantErr     bool
		checkResult bool
	}{
		{
			name: "正常查询-基本参数",
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含ActID",
			params: data.OperatorListReq{
				InstID:   100,
				ActID:    200,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-包含操作类型筛选",
			params: data.OperatorListReq{
				InstID:       100,
				OperatorType: 1, // 开启
				PageNo:       1,
				PageSize:     10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "正常查询-全部参数",
			params: data.OperatorListReq{
				InstID:       100,
				ActID:        200,
				OperatorType: 2, // 关闭
				PageNo:       1,
				PageSize:     10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-无分页参数",
			params: data.OperatorListReq{
				InstID: 100,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-最小分页",
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 1,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-大分页",
			params: data.OperatorListReq{
				InstID:   100,
				PageNo:   1,
				PageSize: 100,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-ActID为0",
			params: data.OperatorListReq{
				InstID:   100,
				ActID:    0,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-操作类型为0",
			params: data.OperatorListReq{
				InstID:       100,
				OperatorType: 0,
				PageNo:       1,
				PageSize:     10,
			},
			wantErr:     false,
			checkResult: true,
		},
		{
			name: "边界测试-负数InstID",
			params: data.OperatorListReq{
				InstID:   -1,
				PageNo:   1,
				PageSize: 10,
			},
			wantErr:     false,
			checkResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := actService.OperatorList(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("OperatorList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.checkResult {
				// 检查返回结果的基本结构
				if resp.List == nil {
					t.Errorf("OperatorList() List should not be nil")
				}
				if resp.Total < 0 {
					t.Errorf("OperatorList() Total should not be negative, got %d", resp.Total)
				}
				// 如果有分页参数，检查返回的数据量
				if tt.params.PageSize > 0 && len(resp.List) > tt.params.PageSize {
					t.Errorf("OperatorList() returned more items than PageSize, expected <= %d, got %d", tt.params.PageSize, len(resp.List))
				}
			}
		})
	}
}

func TestService_BatchUpdateAct(t *testing.T) {
	tests := []struct {
		name    string
		params  data.BatchUpdateActReq
		wantErr bool
	}{
		{
			name: "正常批量更新活动-开启状态",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{1001, 1002, 1003},
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常批量更新活动-关闭状态",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{1004, 1005},
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "单个活动更新",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{1006},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-空活动ID列表",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{},
				OperatorType: 1,
			},
			wantErr: false, // 空列表不会报错，但不会更新任何记录
		},
		{
			name: "边界测试-负数活动ID",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{-1, -2},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-无效操作类型",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{1007},
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false,
		},
		{
			name: "边界测试-操作类型为0",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{1008},
				OperatorType: 0,
			},
			wantErr: false,
		},
		{
			name: "大批量更新测试",
			params: data.BatchUpdateActReq{
				InstActIds:   []int64{2001, 2002, 2003, 2004, 2005, 2006, 2007, 2008, 2009, 2010},
				OperatorType: 1,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.BatchUpdateAct(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateAct() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，记录成功信息
			if !tt.wantErr {
				t.Logf("BatchUpdateAct() executed successfully for %d activity records", len(tt.params.InstActIds))
			}
		})
	}
}

func TestService_FlushHistoryInstActConfig(t *testing.T) {
	tests := []struct {
		name    string
		params  data.FlushHistoryInstActConfigReq
		wantErr bool
	}{
		{
			name:    "正常刷新历史数据",
			params:  data.FlushHistoryInstActConfigReq{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.FlushHistoryInstActConfig(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("FlushHistoryInstActConfig() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，记录成功信息
			if !tt.wantErr {
				t.Logf("FlushHistoryInstActConfig() executed successfully")
			}
		})
	}
}

// 测试参数验证和边界条件
func TestService_ParameterValidation(t *testing.T) {
	t.Run("InstList参数验证", func(t *testing.T) {
		// 测试极端分页参数
		resp, err := actService.InstList(ctx, data.ListInstReq{
			AppID:    1,
			PageNo:   -1, // 负数页码
			PageSize: -10, // 负数页大小
		})
		if err != nil {
			t.Errorf("InstList() with negative pagination should not error, got %v", err)
		}
		if resp.List == nil {
			t.Errorf("InstList() List should not be nil")
		}
	})

	t.Run("ActList参数验证", func(t *testing.T) {
		// 测试极端分页参数
		resp, err := actService.ActList(ctx, data.ActListReq{
			InstID:   100,
			PageNo:   -1, // 负数页码
			PageSize: -10, // 负数页大小
		})
		if err != nil {
			t.Errorf("ActList() with negative pagination should not error, got %v", err)
		}
		if resp.List == nil {
			t.Errorf("ActList() List should not be nil")
		}
	})

	t.Run("OperatorList参数验证", func(t *testing.T) {
		// 测试极端分页参数
		resp, err := actService.OperatorList(ctx, data.OperatorListReq{
			InstID:   100,
			PageNo:   -1, // 负数页码
			PageSize: -10, // 负数页大小
		})
		if err != nil {
			t.Errorf("OperatorList() with negative pagination should not error, got %v", err)
		}
		if resp.List == nil {
			t.Errorf("OperatorList() List should not be nil")
		}
	})
}

// 测试数据一致性
func TestService_DataConsistency(t *testing.T) {
	t.Run("InstList数据一致性检查", func(t *testing.T) {
		resp, err := actService.InstList(ctx, data.ListInstReq{
			AppID:    1,
			PageNo:   1,
			PageSize: 5,
		})
		if err != nil {
			t.Errorf("InstList() error = %v", err)
			return
		}

		// 检查数据一致性
		for i, item := range resp.List {
			if item.AppID <= 0 {
				t.Errorf("InstList() item[%d] AppID should be positive, got %d", i, item.AppID)
			}
			if item.InstID <= 0 {
				t.Errorf("InstList() item[%d] InstID should be positive, got %d", i, item.InstID)
			}
			if item.Status != 1 && item.Status != 2 {
				t.Errorf("InstList() item[%d] Status should be 1 or 2, got %d", i, item.Status)
			}
		}
	})

	t.Run("ActList数据一致性检查", func(t *testing.T) {
		resp, err := actService.ActList(ctx, data.ActListReq{
			InstID:   100,
			PageNo:   1,
			PageSize: 5,
		})
		if err != nil {
			t.Errorf("ActList() error = %v", err)
			return
		}

		// 检查数据一致性
		for i, item := range resp.List {
			if item.InstID <= 0 {
				t.Errorf("ActList() item[%d] InstID should be positive, got %d", i, item.InstID)
			}
			if item.ActID <= 0 {
				t.Errorf("ActList() item[%d] ActID should be positive, got %d", i, item.ActID)
			}
			if item.Status != 1 && item.Status != 2 {
				t.Errorf("ActList() item[%d] Status should be 1 or 2, got %d", i, item.Status)
			}
		}
	})

	t.Run("OperatorList数据一致性检查", func(t *testing.T) {
		resp, err := actService.OperatorList(ctx, data.OperatorListReq{
			InstID:   100,
			PageNo:   1,
			PageSize: 5,
		})
		if err != nil {
			t.Errorf("OperatorList() error = %v", err)
			return
		}

		// 检查数据一致性
		for i, item := range resp.List {
			if item.InstID <= 0 {
				t.Errorf("OperatorList() item[%d] InstID should be positive, got %d", i, item.InstID)
			}
			if item.OperatorType != 1 && item.OperatorType != 2 {
				t.Errorf("OperatorList() item[%d] OperatorType should be 1 or 2, got %d", i, item.OperatorType)
			}
			if item.Operator == "" {
				t.Errorf("OperatorList() item[%d] Operator should not be empty", i)
			}
		}
	})
}

// 测试分页功能
func TestService_PaginationFeatures(t *testing.T) {
	t.Run("InstList分页测试", func(t *testing.T) {
		// 获取第一页
		resp1, err := actService.InstList(ctx, data.ListInstReq{
			AppID:    1,
			PageNo:   1,
			PageSize: 2,
		})
		if err != nil {
			t.Errorf("InstList() page 1 error = %v", err)
			return
		}

		// 获取第二页
		resp2, err := actService.InstList(ctx, data.ListInstReq{
			AppID:    1,
			PageNo:   2,
			PageSize: 2,
		})
		if err != nil {
			t.Errorf("InstList() page 2 error = %v", err)
			return
		}

		// 检查分页逻辑
		if resp1.Total != resp2.Total {
			t.Errorf("InstList() Total should be same across pages, page1=%d, page2=%d", resp1.Total, resp2.Total)
		}

		// 如果有足够的数据，检查页面间数据不重复
		if len(resp1.List) > 0 && len(resp2.List) > 0 {
			for _, item1 := range resp1.List {
				for _, item2 := range resp2.List {
					if item1.ID == item2.ID {
						t.Errorf("InstList() found duplicate ID %d across pages", item1.ID)
					}
				}
			}
		}
	})

	t.Run("ActList分页测试", func(t *testing.T) {
		// 获取第一页
		resp1, err := actService.ActList(ctx, data.ActListReq{
			InstID:   100,
			PageNo:   1,
			PageSize: 2,
		})
		if err != nil {
			t.Errorf("ActList() page 1 error = %v", err)
			return
		}

		// 获取第二页
		resp2, err := actService.ActList(ctx, data.ActListReq{
			InstID:   100,
			PageNo:   2,
			PageSize: 2,
		})
		if err != nil {
			t.Errorf("ActList() page 2 error = %v", err)
			return
		}

		// 检查分页逻辑
		if resp1.Total != resp2.Total {
			t.Errorf("ActList() Total should be same across pages, page1=%d, page2=%d", resp1.Total, resp2.Total)
		}
	})
}

// 测试批量操作的特殊场景
func TestService_BatchOperationsSpecialCases(t *testing.T) {
	t.Run("BatchAddInst参数验证", func(t *testing.T) {
		// 测试必填参数缺失的情况
		err := actService.BatchAddInst(ctx, data.BatchAddInstReq{
			// AppID 缺失
			InstIDs:      []int64{100},
			OperatorType: 1,
		})
		if err != nil {
			t.Logf("BatchAddInst() with missing AppID handled correctly: %v", err)
		}
	})

	t.Run("BatchAddAct参数验证", func(t *testing.T) {
		// 测试必填参数缺失的情况
		err := actService.BatchAddAct(ctx, data.BatchAddActReq{
			// ActID 缺失
			InstIDs:      []int64{100},
			OperatorType: 1,
		})
		if err != nil {
			t.Logf("BatchAddAct() with missing ActID handled correctly: %v", err)
		}
	})

	t.Run("批量操作用户信息测试", func(t *testing.T) {
		// 创建一个没有用户信息的上下文
		emptyCtx := &gin.Context{}

		// 测试在没有用户信息的情况下执行批量操作
		err := actService.BatchAddInst(emptyCtx, data.BatchAddInstReq{
			AppID:        1,
			InstIDs:      []int64{100},
			OperatorType: 1,
		})
		// 应该不会报错，但操作人信息会为空
		if err != nil {
			t.Logf("BatchAddInst() without user info: %v", err)
		} else {
			t.Logf("BatchAddInst() executed without user info")
		}
	})
}

// 测试批量操作的数据一致性
func TestService_BatchOperationsDataConsistency(t *testing.T) {
	t.Run("批量添加机构数据一致性", func(t *testing.T) {
		params := data.BatchAddInstReq{
			AppID:        1,
			InstIDs:      []int64{1001, 1002, 1003},
			OperatorType: 1,
		}

		err := actService.BatchAddInst(ctx, params)
		if err != nil {
			t.Errorf("BatchAddInst() failed: %v", err)
			return
		}

		// 检查参数的一致性
		if len(params.InstIDs) != 3 {
			t.Errorf("Expected 3 institutions, got %d", len(params.InstIDs))
		}
		if params.OperatorType != 1 {
			t.Errorf("Expected OperatorType 1, got %d", params.OperatorType)
		}
	})

	t.Run("批量添加活动数据一致性", func(t *testing.T) {
		params := data.BatchAddActReq{
			ActID:        2001,
			InstIDs:      []int64{100, 200},
			OperatorType: 2,
		}

		err := actService.BatchAddAct(ctx, params)
		if err != nil {
			t.Errorf("BatchAddAct() failed: %v", err)
			return
		}

		// 检查参数的一致性
		if params.ActID != 2001 {
			t.Errorf("Expected ActID 2001, got %d", params.ActID)
		}
		if len(params.InstIDs) != 2 {
			t.Errorf("Expected 2 institutions, got %d", len(params.InstIDs))
		}
	})

	t.Run("批量更新数据一致性", func(t *testing.T) {
		params := data.BatchUpdateActReq{
			InstActIds:   []int64{3001, 3002, 3003},
			OperatorType: 1,
		}

		err := actService.BatchUpdateAct(ctx, params)
		if err != nil {
			t.Errorf("BatchUpdateAct() failed: %v", err)
			return
		}

		// 检查参数的一致性
		if len(params.InstActIds) != 3 {
			t.Errorf("Expected 3 activity records, got %d", len(params.InstActIds))
		}
		if params.OperatorType != 1 {
			t.Errorf("Expected OperatorType 1, got %d", params.OperatorType)
		}
	})
}

// 测试 BatchUpsertInst 方法
func TestService_BatchUpsertInst(t *testing.T) {
	tests := []struct {
		name    string
		params  data.BatchAddInstReq
		wantErr bool
	}{
		{
			name: "正常批量Upsert-开启状态",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{2001, 2002, 2003},
				OperatorType: 1, // 开启
			},
			wantErr: false,
		},
		{
			name: "正常批量Upsert-关闭状态",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{2004, 2005},
				OperatorType: 2, // 关闭
			},
			wantErr: false,
		},
		{
			name: "混合场景-新增和更新",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{100, 2006}, // 100可能已存在，2006是新的
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "单个机构Upsert",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{2007},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-空机构列表",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{},
				OperatorType: 1,
			},
			wantErr: false, // 空列表不会报错，直接返回
		},
		{
			name: "边界测试-AppID为0",
			params: data.BatchAddInstReq{
				AppID:        0,
				InstIDs:      []int64{2008},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-负数机构ID",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{-1, -2},
				OperatorType: 1,
			},
			wantErr: false,
		},
		{
			name: "边界测试-无效操作类型",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{2009},
				OperatorType: 3, // 无效的操作类型
			},
			wantErr: false,
		},
		{
			name: "大批量Upsert测试",
			params: data.BatchAddInstReq{
				AppID:        1,
				InstIDs:      []int64{3001, 3002, 3003, 3004, 3005, 3006, 3007, 3008, 3009, 3010},
				OperatorType: 2,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := actService.BatchUpsertInst(ctx, tt.params)

			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpsertInst() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 如果没有错误，记录成功信息
			if !tt.wantErr {
				t.Logf("BatchUpsertInst() executed successfully for %d institutions", len(tt.params.InstIDs))
			}
		})
	}
}

// 测试 BatchUpsertInst 的特殊场景
func TestService_BatchUpsertInstSpecialCases(t *testing.T) {
	t.Run("Upsert操作数据一致性检查", func(t *testing.T) {
		params := data.BatchAddInstReq{
			AppID:        1,
			InstIDs:      []int64{4001, 4002},
			OperatorType: 1,
		}

		// 第一次执行，应该是插入
		err := actService.BatchUpsertInst(ctx, params)
		if err != nil {
			t.Errorf("First BatchUpsertInst() failed: %v", err)
			return
		}

		// 第二次执行，应该是更新
		params.OperatorType = 2 // 改为关闭
		err = actService.BatchUpsertInst(ctx, params)
		if err != nil {
			t.Errorf("Second BatchUpsertInst() failed: %v", err)
			return
		}

		t.Logf("BatchUpsertInst() consistency test passed")
	})

	t.Run("无用户信息情况下Upsert", func(t *testing.T) {
		// 创建一个没有用户信息的上下文
		emptyCtx := &gin.Context{}

		err := actService.BatchUpsertInst(emptyCtx, data.BatchAddInstReq{
			AppID:        1,
			InstIDs:      []int64{4003},
			OperatorType: 1,
		})
		// 应该不会报错，但操作人信息会为空
		if err != nil {
			t.Logf("BatchUpsertInst() without user info: %v", err)
		} else {
			t.Logf("BatchUpsertInst() executed without user info")
		}
	})

	t.Run("参数验证测试", func(t *testing.T) {
		// 测试必填参数缺失的情况
		err := actService.BatchUpsertInst(ctx, data.BatchAddInstReq{
			// AppID 缺失
			InstIDs:      []int64{4004},
			OperatorType: 1,
		})
		if err != nil {
			t.Logf("BatchUpsertInst() with missing AppID handled correctly: %v", err)
		}
	})
}
