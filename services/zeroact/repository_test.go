package zeroact

import (
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"go-hetu/helpers"
	"go-hetu/models"
	"go-hetu/services/zeroact/data"
	"os"
	"testing"
)

var repo Repository
var ser service
var ctx *gin.Context

func TestMain(t *testing.M) {
	env.SetRootPath("../..")
	helpers.PreInit()
	helpers.InitMysql()
	repo = NewRepository(helpers.MysqlClient)
	ser = service{repo: NewRepository(helpers.MysqlClient)}
	ctx = &gin.Context{}
	os.Exit(t.Run())
}

func TestRepository_Insert(t *testing.T) {
	data := models.ZeroActivity{
		ID:           0,
		ActyName:     "0转正测试任务1",
		TaskID:       1,
		TaskName:     "群控h5任务name",
		URL:          "url",
		Deleted:      0,
		OperatorID:   0,
		OperatorName: "",
		CreateTime:   0,
		UpdateTime:   0,
	}
	err := repo.Insert(ctx, &data)
	fmt.Println(err)
}

func TestRepository_Search(t *testing.T) {
	cond := data.SearchCond{
		TaskName:   "hyr",
		TaskID:     0,
		NormalPage: &models.NormalPage{},
	}
	total, list, err := repo.Search(ctx, cond)
	fmt.Printf("total=[%v], list=[%+v], err=[%v]", total, list, err)
}

func TestRepository_Delete(t *testing.T) {
	err := repo.Delete(ctx, models.ID(3))
	fmt.Println(err)
}
