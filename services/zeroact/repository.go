package zeroact

import (
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"go-hetu/components"
	"go-hetu/models"
	"go-hetu/services/zeroact/data"
	"gorm.io/gorm"
)

type Repository interface {
	Insert(ctx *gin.Context, activity *models.ZeroActivity) (err error)
	Search(ctx *gin.Context, cond data.SearchCond) (total int64, list []models.ZeroActivity, err error)
	Delete(ctx *gin.Context, actyID models.ID) (err error)
	UpdateById(ctx *gin.Context, actyID models.ID, fields map[string]interface{}) (err error)
	GetByActyID(ctx *gin.Context, actyID models.ID) (info models.ZeroActivity, err error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return repository{db: db}
}

func (r repository) Insert(ctx *gin.Context, activity *models.ZeroActivity) (err error) {
	err = r.db.WithContext(ctx).Model(&models.ZeroActivity{}).Create(activity).Error
	if err != nil {
		zlog.Warnf(ctx, "[Insert] failed:activity=[%+v], err=[%v]", activity, err)
		err = components.ErrorDbInsert.Sprintf("添加0转正活动任务失败")
		return err
	}
	return nil
}

func (r repository) Search(ctx *gin.Context, cond data.SearchCond) (total int64, list []models.ZeroActivity, err error) {
	db := r.db.WithContext(ctx).Model(&models.ZeroActivity{}).Scopes(models.WithNotDeleted)
	if len(cond.TaskName) > 0 {
		db = db.Where("task_name like ?", "%"+cond.TaskName+"%")
	}
	if len(cond.ActyName) > 0 {
		db = db.Where("acty_name like ?", "%"+cond.ActyName+"%")
	}
	if cond.TaskID > 0 {
		db = db.Where("task_id = ? or s_task_id = ? or a_task_id = ? or b1_task_id = ? or b2_task_id = ? or c_task_id = ? or unknown_task_id = ?", cond.TaskID, cond.TaskID, cond.TaskID, cond.TaskID, cond.TaskID, cond.TaskID, cond.TaskID)
	}
	if len(cond.Cadres) > 0 {
		db = db.Where("operator_name in ?", cond.Cadres)
	}
	err = db.Count(&total).Error
	if err != nil {
		zlog.Warnf(ctx, "[Search] db.Count failed: cond=[%+v], err=[%v]", cond, err)
		err = components.ErrorDbSelect.Sprintf("搜索0转正业务失败")
		return total, nil, err
	}
	cond.Order = "id desc"
	db = db.Scopes(models.NormalPaginate(cond.NormalPage))

	err = db.Find(&list).Error
	if err != nil {
		zlog.Warnf(ctx, "[Search] failed: cond=[%+v], err=[%v]", cond, err)
		err = components.ErrorDbSelect.Sprintf("搜索0转正业务失败")
		return total, nil, err
	}
	return total, list, nil
}

func (r repository) Delete(ctx *gin.Context, actyID models.ID) (err error) {
	err = r.db.WithContext(ctx).
		Model(&models.ZeroActivity{}).
		Scopes(models.WithNotDeleted).
		Where("id = ?", actyID).
		Update("deleted", models.DeletedYes).Error
	if err != nil {
		zlog.Warnf(ctx, "[Delete] failed: actyID=[%v], err=[%v]", actyID, err)
		components.ErrorDbUpdate.Sprintf("删除0转正活动失败")
		return err
	}
	return nil
}

func (r repository) UpdateById(ctx *gin.Context, actyID models.ID, fields map[string]interface{}) (err error) {
	err = r.db.WithContext(ctx).
		Model(&models.ZeroActivity{}).
		Scopes(models.WithNotDeleted).
		Where("id = ?", actyID).
		Updates(fields).Error
	if err != nil {
		zlog.Warnf(ctx, "[UpdateById] failed: actyID=[%v], err=[%v]", actyID, err)
		components.ErrorDbUpdate.Sprintf("修改0转正活动失败")
		return err
	}
	return nil
}

func (r repository) GetByActyID(ctx *gin.Context, actyID models.ID) (info models.ZeroActivity, err error) {
	err = r.db.WithContext(ctx).
		Model(&models.ZeroActivity{}).
		Scopes(models.WithNotDeleted).
		Where("id = ?", actyID).
		Take(&info).Error
	if err != nil {
		zlog.Warnf(ctx, "[GetByActyID] failed: actyID=[%v], err=[%v]", actyID, err)
		err = components.ErrorDbSelect.Sprintf("0转正活动信息获取失败")
		return info, err
	}
	return info, nil
}
