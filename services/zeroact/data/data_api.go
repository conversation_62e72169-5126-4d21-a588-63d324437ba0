package data

import "go-hetu/models"

type AddActivityParam struct {
	ActyName      string `json:"actyName" binding:"required"` // 活动名称
	TaskID        int    `json:"taskId"`                      // 群控H5任务ID
	CubeURL       string `json:"cubeUrl" binding:"required"`  // 魔方活动链接
	IsFlow        int    `json:"isFlow"`                      // 是否分流
	FlowBusiness  int    `json:"flowBusiness"`                // 分流业务线
	STaskID       int    `json:"sTaskId"`                     // 群控H5任务ID
	ATaskID       int    `json:"aTaskId"`                     // 群控H5任务ID
	B1TaskID      int    `json:"b1TaskId"`                    // 群控H5任务ID
	B2TaskID      int    `json:"b2TaskId"`                    // 群控H5任务ID
	CTaskID       int    `json:"cTaskId"`                     // 群控H5任务ID
	UnknownTaskID int    `json:"unknownTaskId"`               // 群控H5任务ID
}

type UpdateActivityParam struct {
	ActID    models.ID `json:"actId" binding:"required"`    // 活动ID
	ActyName string    `json:"actyName" binding:"required"` // 活动名称
}

type SearchActivityParam struct {
	TaskName string `form:"taskName"`
	ActyName string `form:"actyName"`
	TaskID   int    `form:"taskId"`
	Page     int    `form:"page" binding:"required"`
	PageSize int    `form:"pageSize" binding:"required"`
}

type GetActivityParam struct {
	ActID models.ID `json:"actId" binding:"required"` // 活动ID
}

type GetActivityResp struct {
	ActID         int    `json:"actId" binding:"required"`    // 活动ID
	ActyName      string `json:"actyName" binding:"required"` // 活动名称
	TaskID        int    `json:"taskId" binding:"required"`   // 群控H5任务ID
	CubeURL       string `json:"cubeUrl" binding:"required"`  // 魔方活动链接
	IsFlow        int    `json:"isFlow"`                      // 是否分流
	FlowBusiness  int    `json:"flowBusiness"`                // 分流业务线
	STaskID       int    `json:"sTaskId"`                     // 群控H5任务ID
	ATaskID       int    `json:"aTaskId"`                     // 群控H5任务ID
	B1TaskID      int    `json:"b1TaskId"`                    // 群控H5任务ID
	B2TaskID      int    `json:"b2TaskId"`                    // 群控H5任务ID
	CTaskID       int    `json:"cTaskId"`                     // 群控H5任务ID
	UnknownTaskID int    `json:"unknownTaskId"`               // 群控H5任务ID
}

type SearchActivityResp struct {
	Page     int                  `json:"page"`
	PageSize int                  `json:"pageSize"`
	Total    int                  `json:"total"`
	List     []SearchActivityItem `json:"list"`
}

type SearchActivityItem struct {
	ActyID        int    `json:"actyId"`   // 活动ID
	ActyName      string `json:"actyName"` // 活动名字
	ActUrl        string `json:"actUrl"`
	IsFlow        int    `json:"isFlow"`        // 群控h5任务ID
	TaskID        string `json:"taskId"`        // 群控h5任务ID
	TaskName      string `json:"taskName"`      // 群控h5任务名称
	OperatorName  string `json:"operatorName"`  // 创建人
	FlowBusiness  int    `json:"flowBusiness"`  // 分流业务线
	STaskID       int    `json:"sTaskId"`       // 群控H5任务ID
	ATaskID       int    `json:"aTaskId"`       // 群控H5任务ID
	B1TaskID      int    `json:"b1TaskId"`      // 群控H5任务ID
	B2TaskID      int    `json:"b2TaskId"`      // 群控H5任务ID
	CTaskID       int    `json:"cTaskId"`       // 群控H5任务ID
	UnknownTaskID int    `json:"unknownTaskId"` // 群控H5任务ID
}

type ListChannelParam struct {
	ActChannelParam
	Pn int `json:"pn"`
	Ps int `json:"ps"`
}

type ActChannelParam struct {
	ActId        int    `json:"actId"`
	ChannelIds   string `json:"channelIds"`
	ChannelName  string `json:"channelName"`
	ChannelState int    `json:"channelState"`
}

type GenUrlParam struct {
	ActId     int `json:"actId"`
	ChannelId int `json:"channelId"`
	UrlType   int `json:"urlType"`
}
