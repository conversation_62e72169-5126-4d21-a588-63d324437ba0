package zeroact

import (
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"go-hetu/services/zeroact/data"
	"testing"
)

func TestService_AddActivity(t *testing.T) {
	param := data.AddActivityParam{
		ActyName: "测试",
		TaskID:   1611471,
		CubeURL:  "testUrl",
	}
	err := ser.AddActivity(ctx, param)
	fmt.Println(err)
}

func TestService_SearchActivity(t *testing.T) {
	res, err := ser.SearchActivity(ctx, data.SearchActivityParam{
		TaskName: "",
		TaskID:   0,
		Page:     0,
		PageSize: 0,
	})
	fmt.Println(res, err)
}

func TestService_GetChannelAll(t *testing.T) {
	// 1611471, 1611468
	res, err := ser.GetChannelAll(ctx, 1611471)
	fmt.Printf("res=[%+v], err=[%v]", res, err)
}

func TestService_cubeURLCheck(t *testing.T) {
	url := "https://tf.zuoyebang.com/static/hy/cornucopia/10353701.html?actId=7072510423280709772&groupId=1&pageId=10353701"
	if env.GetRunEnv() == env.RunEnvTest {
		url = "https://cube-etrade-cc.suanshubang.cc/cube/act/display/out?actId=7073998340485393552&groupId=1&pageId=3947679"
	}
	res, scs := cubeURLCheck(ctx, url)
	fmt.Printf("res=[%v], scs=[%v]", res, scs)
}
