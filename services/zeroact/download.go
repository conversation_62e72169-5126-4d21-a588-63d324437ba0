package zeroact

import (
	"bytes"
	"encoding/csv"
	"errors"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/skip2/go-qrcode"
	"github.com/spf13/cast"
	"go-hetu/api/api_moat"
	"go-hetu/api/api_wxqk"
	"go-hetu/helpers"
	"go-hetu/models"
	"go-hetu/services/zeroact/data"
	"net/url"
	"strconv"
	"strings"
)

const (
	exeStatusWaiting = 1 //待执行
	exeStatusDoing   = 2 //执行中
	exeStatusEnd     = 3 //已结束
)

func ExecDownloadTaskByAct(ctx *gin.Context, task models.ZeroActivityDownload) {
	helpers.MysqlClient.WithContext(ctx).Model(&models.ZeroActivityDownload{Id: task.Id}).Update("status", exeStatusDoing)

	var updates = map[string]interface{}{
		"status": exeStatusEnd,
	}
	if lines, err := getActDataLines(ctx, task); err != nil {
		updates["result_msg"] = fmt.Sprintf("失败：%s", err.Error())
	} else {
		// 写数据进csv，并上传cos
		buf := &bytes.Buffer{}
		buf.WriteString("\xEF\xBB\xBF")
		w := csv.NewWriter(buf)
		_ = w.WriteAll(lines)
		var (
			content  = string(buf.Bytes())
			filename = fmt.Sprintf("tf_%s_%s", task.FileName, utils.Md5(content))
		)
		if dwUrl, err := helpers.Bucket.UploadFileContent(ctx, content, filename, "csv"); err != nil {
			updates["status"] = exeStatusEnd
			updates["result_msg"] = fmt.Sprintf("失败：%s", err.Error())
		} else {
			parseUrl, _ := url.Parse(dwUrl)
			updates = map[string]interface{}{
				"status":         exeStatusEnd,
				"result_msg":     "成功",
				"file_url":       fmt.Sprintf("https://zyb-toufang.cdnjtzy.com%s", parseUrl.Path),
				"file_data_size": len(lines) - 1,
			}
		}
	}
	if len(updates) > 0 {
		helpers.MysqlClient.WithContext(ctx).Model(&models.ZeroActivityDownload{}).Where("id=?", task.Id).Updates(updates)
	}
}

func getActDataLines(ctx *gin.Context, task models.ZeroActivityDownload) ([][]string, error) {
	var act models.ZeroActivity
	helpers.MysqlClient.WithContext(ctx).Where("id=?", task.ActId).Scopes(models.WithNotDeleted).Take(&act)
	if act.ID == 0 {
		return nil, errors.New("0元活动不存在")
	}
	cubeActId, ok := CubeURLCheck(ctx, act.URL)
	if !ok {
		return nil, errors.New("0元活动任务魔方页面链接有误")
	}
	taskId := act.TaskID
	if act.IsFlow == 1 {
		taskId = act.STaskID
	}
	var extParam data.ListChannelParam
	_ = jsoniter.Unmarshal([]byte(task.Ext), &extParam)
	var (
		param = api_wxqk.ListChannelParam{
			PageNo:       1,
			PageSize:     50,
			H5TaskId:     taskId,
			ChannelIds:   extParam.ChannelIds,
			ChannelName:  extParam.ChannelName,
			ChannelState: extParam.ChannelState,
		}
		dataList []api_wxqk.ChannelListItem
	)
	for {
		resp, err := api_wxqk.ListChannelInfo(ctx, param)
		if err != nil {
			zlog.Warnf(ctx, "0元活动查询h5渠道异常：%s", err.Error())
			return nil, err
		}
		if len(resp.List) > 0 {
			dataList = append(dataList, resp.List...)
		}
		if resp.Meta.Total <= param.PageNo*param.PageSize || len(dataList) >= 500 { //限制不超500
			break
		}
		param.PageNo++
	}

	lines := [][]string{{"活动ID", "魔方活动ID", "H5任务ID", "H5任务名称", "渠道ID", "渠道名称", "渠道状态", "河图投放链接", "短链", "二维码"}}
	for _, info := range dataList {
		taskIds := ""
		taskNameShow := ""
		if act.IsFlow == 1 {
			taskIdMap := make(map[int]struct{})
			taskIdSli := make([]string, 0)
			taskNameSli := make([]string, 0)
			taskIdSli = append(taskIdSli, strconv.Itoa(act.STaskID))
			taskNameSli = append(taskNameSli, act.STaskName)
			taskIdMap[act.STaskID] = struct{}{}
			if _, ok := taskIdMap[act.ATaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(act.ATaskID))
				taskNameSli = append(taskNameSli, act.ATaskName)
				taskIdMap[act.ATaskID] = struct{}{}
			}
			if _, ok := taskIdMap[act.B1TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(act.B1TaskID))
				taskNameSli = append(taskNameSli, act.B1TaskName)
				taskIdMap[act.B1TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[act.B2TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(act.B2TaskID))
				taskNameSli = append(taskNameSli, act.B2TaskName)
				taskIdMap[act.B2TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[act.CTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(act.CTaskID))
				taskNameSli = append(taskNameSli, act.CTaskName)
				taskIdMap[act.CTaskID] = struct{}{}
			}
			if _, ok := taskIdMap[act.UnknownTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(act.UnknownTaskID))
				taskNameSli = append(taskNameSli, act.UnknownTaskName)
				taskIdMap[act.UnknownTaskID] = struct{}{}
			}
			taskIds = strings.Join(taskIdSli, ",")
			taskNameShow = strings.Join(taskNameSli, ",")
		} else {
			taskIds = strconv.Itoa(act.TaskID)
			taskNameShow = act.TaskName
		}

		longUrl := ""
		if act.IsFlow == 1 {
			longUrl = fmt.Sprintf("%s&flActId=%d&wxQdId=%d", act.URL, act.ID, info.ChannelID)
		} else {
			longUrl = fmt.Sprintf("%s&taskId=%d&wxQdId=%d", act.URL, act.TaskID, info.ChannelID)
		}
		shortUrl := genShortUrl(ctx, longUrl)
		qrCodeUrl := genQrcodeUrl(ctx, longUrl)
		cs := "开启"
		if info.ChannelState == 1 {
			cs = "关闭"
		}
		line := []string{
			cast.ToString(act.ID),
			fmt.Sprintf("%s\t", cast.ToString(cubeActId)),
			fmt.Sprintf("%s\t", taskIds),
			fmt.Sprintf("%s\t", taskNameShow),
			fmt.Sprintf("%s\t", cast.ToString(info.ChannelID)),
			fmt.Sprintf("%s\t", info.ChannelName),
			fmt.Sprintf("%s\t", cs),
			longUrl,
			shortUrl,
			qrCodeUrl,
		}
		lines = append(lines, line)
	}
	return lines, nil
}

func genShortUrl(ctx *gin.Context, longUrl string) string {
	sl, err := api_moat.CreateShortUrl(ctx, longUrl)
	if err != nil {
		zlog.Warnf(ctx, "生成短链异常: %s", err.Error())
		return ""
	}
	return sl
}

func genQrcodeUrl(ctx *gin.Context, longUrl string) string {
	pngBytes, err := qrcode.Encode(longUrl, qrcode.Highest, 342)
	if err != nil {
		zlog.Warnf(ctx, "生成二维码异常: %s", err.Error())
		return ""
	}
	content := string(pngBytes)
	dwUrl, err := helpers.Bucket.UploadFileContent(ctx, content, utils.Md5(content), "png")
	if err != nil {
		zlog.Warnf(ctx, "二维码图片上传失败: %s", err.Error())
		return ""
	}
	parseUrl, err := url.Parse(dwUrl)
	if err != nil || parseUrl == nil {
		zlog.Warnf(ctx, "二维码图片上传失败: err=[%v], paraseUrl=[%v]", err, parseUrl)
		return ""
	}
	qrcodeUrl := fmt.Sprintf("https://zyb-toufang.cdnjtzy.com%s", parseUrl.Path)
	return qrcodeUrl
}
