package zeroact

import (
	"bytes"
	"encoding/csv"
	"fmt"
	libUtils "git.zuoyebang.cc/ad/gocommons/utils"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go-hetu/api/api_wxqk"
	"go-hetu/api/api_wxqk_go"
	"go-hetu/components"
	dv "go-hetu/data"
	"go-hetu/helpers"
	"go-hetu/middleware"
	"go-hetu/models"
	data2 "go-hetu/services/zeroact/data"
	"go-hetu/utils"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
)

type Service interface {
	AddActivity(ctx *gin.Context, param data2.AddActivityParam) (err error)
	SearchActivity(ctx *gin.Context, param data2.SearchActivityParam) (resp data2.SearchActivityResp, err error)
	GetActivity(ctx *gin.Context, param data2.GetActivityParam) (resp data2.GetActivityResp, err error)
	UpdateActivity(ctx *gin.Context, param data2.UpdateActivityParam) (err error)
	DeleteActivity(ctx *gin.Context, actyID models.ID) (err error)
	DownloadChannel(ctx *gin.Context, actyID models.ID) (err error)
}

func NewService() Service {
	return service{repo: NewRepository(helpers.MysqlClient)}
}

type service struct {
	repo Repository
}

func (s service) AddActivity(ctx *gin.Context, param data2.AddActivityParam) (err error) {
	// 校验url是否合法
	_, ok := CubeURLCheck(ctx, param.CubeURL)
	if !ok {
		zlog.Warnf(ctx, "[AddActivity] cubeURLCheck failed: cubeURL=[%s]", param.CubeURL)
		err = fmt.Errorf("魔方url有误，请检查后重新输入")
		return err
	}
	// 任务ID去重
	taskIdToNameMap := make(map[int]string)
	if param.IsFlow == 1 {
		taskIdToNameMap[param.STaskID] = ""
		taskIdToNameMap[param.ATaskID] = ""
		taskIdToNameMap[param.B1TaskID] = ""
		taskIdToNameMap[param.B2TaskID] = ""
		taskIdToNameMap[param.CTaskID] = ""
		taskIdToNameMap[param.UnknownTaskID] = ""
	} else {
		taskIdToNameMap[param.TaskID] = ""
	}
	// 校验h5任务是否合法
	for tid, _ := range taskIdToNameMap {
		taskInfo, err := api_wxqk_go.GetH5TaskDetail(ctx, tid)
		if err != nil {
			zlog.Warnf(ctx, "[AddActivity] taskID=[%v], err=[%v]", tid, err)
			err = fmt.Errorf("获取群控任务详情失败，请稍后重试。")
			return err
		}
		if taskInfo.TaskID == 0 {
			err = fmt.Errorf("群控任务ID:%d 不存在", tid)
			return err
		}
		taskIdToNameMap[tid] = taskInfo.TaskName
	}
	if param.IsFlow == 1 {
		// 检查活动渠道是否一致
		_, err = checkFlowTaskActChannel(ctx, taskIdToNameMap)
		if err != nil {
			return err
		}
	}
	u := middleware.GetUserInfoFromCtx(ctx)
	actyTmp := models.ZeroActivity{
		ActyName:        param.ActyName,
		TaskID:          param.TaskID,
		TaskName:        taskIdToNameMap[param.TaskID],
		STaskID:         param.STaskID,
		STaskName:       taskIdToNameMap[param.STaskID],
		ATaskID:         param.ATaskID,
		ATaskName:       taskIdToNameMap[param.ATaskID],
		B1TaskID:        param.B1TaskID,
		B1TaskName:      taskIdToNameMap[param.B1TaskID],
		B2TaskID:        param.B2TaskID,
		B2TaskName:      taskIdToNameMap[param.B2TaskID],
		CTaskID:         param.CTaskID,
		CTaskName:       taskIdToNameMap[param.CTaskID],
		UnknownTaskID:   param.UnknownTaskID,
		UnknownTaskName: taskIdToNameMap[param.UnknownTaskID],
		IsFlow:          param.IsFlow,
		FlowBusiness:    param.FlowBusiness,
		URL:             param.CubeURL,
		OperatorID:      models.ID(u.Uid),
		OperatorName:    u.Uname,
	}
	err = s.repo.Insert(ctx, &actyTmp)
	if err != nil {
		return err
	}
	// 判断taskID是否合法
	return nil
}

func (s service) UpdateActivity(ctx *gin.Context, param data2.UpdateActivityParam) (err error) {
	var act models.ZeroActivity
	helpers.MysqlClient.WithContext(ctx).Where("id=?", param.ActID).Take(&act)
	if act.ID == 0 {
		return components.ErrorParamInvalid
	}
	//u := middleware.GetUserInfoFromCtx(ctx)
	pMap := make(map[string]interface{})
	pMap["acty_name"] = param.ActyName
	// 有权限控制，先不改操作人
	//pMap["operator_id"] = models.ID(u.Uid)
	//pMap["operator_name"] = u.Uname
	err = s.repo.UpdateById(ctx, param.ActID, pMap)
	if err != nil {
		zlog.Warnf(ctx, "UpdateActivity err:%v", err)
		return err
	}

	return err
}

func (s service) GetActivity(ctx *gin.Context, param data2.GetActivityParam) (resp data2.GetActivityResp, err error) {
	// 1. 获取活动ID对应详情
	actyInfo, err := s.repo.GetByActyID(ctx, param.ActID)
	if err != nil {
		return resp, err
	}
	if actyInfo.ID == 0 {
		return resp, components.ErrorParamInvalid
	}
	resp.ActID = actyInfo.ID
	resp.ActyName = actyInfo.ActyName
	resp.TaskID = actyInfo.TaskID
	resp.STaskID = actyInfo.STaskID
	resp.ATaskID = actyInfo.ATaskID
	resp.B1TaskID = actyInfo.B1TaskID
	resp.B2TaskID = actyInfo.B2TaskID
	resp.CTaskID = actyInfo.CTaskID
	resp.UnknownTaskID = actyInfo.UnknownTaskID
	resp.IsFlow = actyInfo.IsFlow
	resp.FlowBusiness = actyInfo.FlowBusiness
	return
}

func (s service) SearchActivity(ctx *gin.Context, param data2.SearchActivityParam) (resp data2.SearchActivityResp, err error) {

	authUsers := middleware.GetCtxUserAuthUser(ctx)
	authUsers = libUtils.RemoveRepeatedElement(authUsers)
	cond := data2.SearchCond{
		TaskName: param.TaskName,
		ActyName: param.ActyName,
		TaskID:   param.TaskID,
		Cadres:   authUsers,
		// Size最大为100
		NormalPage: &models.NormalPage{
			No:   param.Page,
			Size: param.PageSize,
		},
	}
	total, list, err := s.repo.Search(ctx, cond)
	if err != nil {
		return resp, err
	}
	resp.Total = int(total)
	resp.Page = cond.No
	resp.PageSize = cond.Size
	for i := range list {
		taskIds := ""
		taskNameShow := ""
		if list[i].IsFlow == 1 {
			taskIdMap := make(map[int]struct{})
			taskIdSli := make([]string, 0)
			taskNameSli := make([]string, 0)
			taskIdSli = append(taskIdSli, strconv.Itoa(list[i].STaskID))
			taskNameSli = append(taskNameSli, list[i].STaskName)
			taskIdMap[list[i].STaskID] = struct{}{}
			if _, ok := taskIdMap[list[i].ATaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(list[i].ATaskID))
				taskNameSli = append(taskNameSli, list[i].ATaskName)
				taskIdMap[list[i].ATaskID] = struct{}{}
			}
			if _, ok := taskIdMap[list[i].B1TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(list[i].B1TaskID))
				taskNameSli = append(taskNameSli, list[i].B1TaskName)
				taskIdMap[list[i].B1TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[list[i].B2TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(list[i].B2TaskID))
				taskNameSli = append(taskNameSli, list[i].B2TaskName)
				taskIdMap[list[i].B2TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[list[i].CTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(list[i].CTaskID))
				taskNameSli = append(taskNameSli, list[i].CTaskName)
				taskIdMap[list[i].CTaskID] = struct{}{}
			}
			if _, ok := taskIdMap[list[i].UnknownTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(list[i].UnknownTaskID))
				taskNameSli = append(taskNameSli, list[i].UnknownTaskName)
				taskIdMap[list[i].UnknownTaskID] = struct{}{}
			}
			taskIds = strings.Join(taskIdSli, ",")
			taskNameShow = strings.Join(taskNameSli, ",")
		} else {
			taskIds = strconv.Itoa(list[i].TaskID)
			taskNameShow = list[i].TaskName
		}
		resp.List = append(resp.List, data2.SearchActivityItem{
			ActyID:        list[i].ID,
			ActyName:      list[i].ActyName,
			ActUrl:        list[i].URL,
			IsFlow:        list[i].IsFlow,
			FlowBusiness:  list[i].FlowBusiness,
			STaskID:       list[i].STaskID,
			ATaskID:       list[i].ATaskID,
			B2TaskID:      list[i].B2TaskID,
			B1TaskID:      list[i].B1TaskID,
			CTaskID:       list[i].CTaskID,
			UnknownTaskID: list[i].UnknownTaskID,
			TaskID:        taskIds,
			TaskName:      taskNameShow,
			OperatorName:  list[i].OperatorName,
		})
	}
	return resp, nil
}

func (s service) DeleteActivity(ctx *gin.Context, actyID models.ID) (err error) {
	err = s.repo.Delete(ctx, actyID)
	if err != nil {
		return err
	}
	return nil
}

func (s service) DownloadChannel(ctx *gin.Context, actyID models.ID) (err error) {
	// 1. 获取活动ID对应详情
	actyInfo, err := s.repo.GetByActyID(ctx, actyID)
	if err != nil {
		return err
	}
	taskId := actyInfo.TaskID
	if actyInfo.IsFlow == 1 {
		taskId = actyInfo.STaskID
	}
	// 2. 获取任务对应渠道ids
	channelList, err := s.GetChannelAll(ctx, taskId)
	if err != nil {
		return err
	}
	// 3. 封装ID
	err = formatCsvContent(ctx, channelList, actyInfo)
	if err != nil {
		return err
	}
	return nil
}

func formatCsvContent(ctx *gin.Context, channelList []api_wxqk.ChannelListItem, actyInfo models.ZeroActivity) (err error) {
	// 从魔方url获取魔方活动ID
	cubeActID, isScs := CubeURLCheck(ctx, actyInfo.URL)
	if isScs == false {
		zlog.Warnf(ctx, "0转正任务魔方页面链接有误，请检查: actyID=[%v], cubeURL=[%v]", actyInfo.ID, actyInfo.URL)
		err = fmt.Errorf("0转正任务魔方页面链接有误，请检查")
		return err
	}
	b := &bytes.Buffer{}
	b.WriteString("\xEF\xBB\xBF") // 写入UTF-8 BOM，防止中文乱码
	w := csv.NewWriter(b)
	title := []string{"活动ID", "魔方活动ID", "H5任务ID", "H5任务名称", "渠道ID", "渠道名称", "河图投放链接"}
	err = w.Write(title)
	if err != nil {
		zlog.Warnf(ctx, "[DownloadChannel] w.write failed: err=[%v]", err)
		return err
	}
	for _, chanInfo := range channelList {
		if chanInfo.ChannelState == api_wxqk.ChannelStateInvalid {
			continue
		}

		taskIds := ""
		taskNameShow := ""
		if actyInfo.IsFlow == 1 {
			taskIdMap := make(map[int]struct{})
			taskIdSli := make([]string, 0)
			taskNameSli := make([]string, 0)
			taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.STaskID))
			taskNameSli = append(taskNameSli, actyInfo.STaskName)
			taskIdMap[actyInfo.STaskID] = struct{}{}
			if _, ok := taskIdMap[actyInfo.ATaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.ATaskID))
				taskNameSli = append(taskNameSli, actyInfo.ATaskName)
				taskIdMap[actyInfo.ATaskID] = struct{}{}
			}
			if _, ok := taskIdMap[actyInfo.B1TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.B1TaskID))
				taskNameSli = append(taskNameSli, actyInfo.B1TaskName)
				taskIdMap[actyInfo.B1TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[actyInfo.B2TaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.B2TaskID))
				taskNameSli = append(taskNameSli, actyInfo.B2TaskName)
				taskIdMap[actyInfo.B2TaskID] = struct{}{}
			}
			if _, ok := taskIdMap[actyInfo.CTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.CTaskID))
				taskNameSli = append(taskNameSli, actyInfo.CTaskName)
				taskIdMap[actyInfo.CTaskID] = struct{}{}
			}
			if _, ok := taskIdMap[actyInfo.UnknownTaskID]; !ok {
				taskIdSli = append(taskIdSli, strconv.Itoa(actyInfo.UnknownTaskID))
				taskNameSli = append(taskNameSli, actyInfo.UnknownTaskName)
				taskIdMap[actyInfo.UnknownTaskID] = struct{}{}
			}
			taskIds = strings.Join(taskIdSli, ",")
			taskNameShow = strings.Join(taskNameSli, ",")
		} else {
			taskIds = strconv.Itoa(actyInfo.TaskID)
			taskNameShow = actyInfo.TaskName
		}

		longUrl := ""
		if actyInfo.IsFlow == 1 {
			longUrl = fmt.Sprintf("%s&flActId=%d&wxQdId=%d", actyInfo.URL, actyInfo.ID, chanInfo.ChannelID)
		} else {
			longUrl = fmt.Sprintf("%s&taskId=%d&wxQdId=%d", actyInfo.URL, actyInfo.TaskID, chanInfo.ChannelID)
		}

		str := []string{
			cast.ToString(actyInfo.ID),
			fmt.Sprintf("%s\t", cast.ToString(cubeActID)),
			fmt.Sprintf("%s\t", taskIds),
			fmt.Sprintf("%s\t", taskNameShow),
			fmt.Sprintf("%s\t", cast.ToString(chanInfo.ChannelID)),
			fmt.Sprintf("%s\t", chanInfo.ChannelName),
			fmt.Sprintf(longUrl),
		}
		err = w.Write(str)
		if err != nil {
			zlog.Warnf(ctx, "[DownloadChannel] w.write failed: err=[%v]", err)
			return err
		}
	}
	w.Flush()

	fileName := fmt.Sprintf("0元活动渠道列表_%s_%s", actyInfo.ActyName, time.Now().Format("2006-01-02"))
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s.csv", fileName))
	ctx.Data(http.StatusOK, "text/csv", b.Bytes())
	return nil
}

// 校验魔方链接是否包含actId
func CubeURLCheck(ctx *gin.Context, cubeUrl string) (cubeActID uint64, isScs bool) {
	// 定义一个正则表达式模式
	pattern := "/static/hy/cornucopia/[\\d]+.html\\?[.*]?actId=(\\d+)[.*]?"
	if env.GetRunEnv() == env.RunEnvTest {
		pattern = "/cube/act/display/out\\?actId=(\\d+)[.*]?"
	}
	// 编译正则表达式
	regex, err := regexp.Compile(pattern)
	if err != nil {
		zlog.Warnf(ctx, "[cubeURLCheck] regexp.Compile failed: cubeUrl=[%v], err=[%v]", cubeUrl, err)
		return cubeActID, false
	}
	// 使用正则表达式查找匹配项
	matches := regex.FindStringSubmatch(cubeUrl)
	if len(matches) < 2 {
		return cubeActID, false
	}
	cubeActID = cast.ToUint64(matches[1])
	if cubeActID <= 0 {
		return cubeActID, false
	}
	return cubeActID, true
}

func (s service) GetChannelAll(ctx *gin.Context, taskID int) (res []api_wxqk.ChannelListItem, err error) {
	page := 1
	pageSize := 20

	apiRes, err := api_wxqk.GetChannelListByTeam(ctx, taskID, page, pageSize, 0)
	if err != nil {
		zlog.Warnf(ctx, "[GetChannelAll] api_wxqk.GetChannelListByTeam failed:taskID=[%v], page=[%v], pageSize=[%v], err=[%v]", taskID, page, pageSize, err)
		return res, err
	}
	if apiRes.Meta.PageNo*apiRes.Meta.PageSize >= apiRes.Meta.Total {
		return apiRes.List, nil
	}

	resChan := make(chan []api_wxqk.ChannelListItem, (apiRes.Meta.Total+pageSize)/pageSize)
	resChanErr := make(chan string, (apiRes.Meta.Total+pageSize)/pageSize)

	resChan <- apiRes.List
	pCnt := 10 // 同时并发数
	cntChan := make(chan struct{}, pCnt)
	page++
	wg := sync.WaitGroup{}
	for ; (page-1)*pageSize < apiRes.Meta.Total; page++ {
		wg.Add(1)
		cntChan <- struct{}{}
		go func(page int) {
			defer wg.Done()
			defer func() {
				<-cntChan
			}()
			tmpRes, wxErr := api_wxqk.GetChannelListByTeam(ctx, taskID, page, pageSize, 0)
			if wxErr != nil {
				zlog.Warnf(ctx, "[GetChannelAll] api_wxqk.GetChannelListByTeam failed:taskID=[%v], page=[%v], pageSize=[%v], err=[%v]", taskID, page, pageSize, wxErr)
				resChanErr <- wxErr.Error()
				return
			}
			resChan <- tmpRes.List
		}(page)
	}
	wg.Wait()
	close(resChan)
	if len(resChanErr) > 0 {
		return nil, fmt.Errorf("获取任务渠道失败，请稍后再试!")
	}
	for items := range resChan {
		res = append(res, items...)
	}
	return res, nil
}

func ListActChannel(ctx *gin.Context, param data2.ListChannelParam) (*dv.Pager, error) {
	var act models.ZeroActivity
	helpers.MysqlClient.WithContext(ctx).Where("id=?", param.ActId).Scopes(models.WithNotDeleted).Take(&act)
	if act.ID == 0 {
		return nil, errors.New("0元活动不存在")
	}
	taskId := act.TaskID
	if act.IsFlow == 1 {
		taskId = act.STaskID
	}
	apiParam := api_wxqk.ListChannelParam{
		H5TaskId:     taskId,
		PageNo:       param.Pn,
		PageSize:     param.Ps,
		ChannelIds:   param.ChannelIds,
		ChannelName:  param.ChannelName,
		ChannelState: param.ChannelState,
	}
	resp, err := api_wxqk.ListChannelInfo(ctx, apiParam)
	if err != nil {
		return nil, err
	}
	if resp.Meta.Total > 0 {
		return dv.WrapPager(int64(resp.Meta.Total), resp.List), nil
	}
	return dv.WrapPager(0, dv.EmptyList{}), nil
}

func GenerateUrl(ctx *gin.Context, param data2.GenUrlParam) (map[string]string, error) {
	var act models.ZeroActivity
	helpers.MysqlClient.WithContext(ctx).Where("id=?", param.ActId).Take(&act)
	if act.ID == 0 {
		return nil, components.ErrorParamInvalid
	}
	longUrl := ""
	if act.IsFlow == 1 {
		longUrl = fmt.Sprintf("%s&flActId=%d&wxQdId=%d", act.URL, act.ID, param.ChannelId)
	} else {
		longUrl = fmt.Sprintf("%s&taskId=%d&wxQdId=%d", act.URL, act.TaskID, param.ChannelId)
	}

	result := make(map[string]string, 0)
	if param.UrlType == 1 { //长链
		result["url"] = longUrl
	} else if param.UrlType == 2 { //短链
		result["shortUrl"] = genShortUrl(ctx, longUrl)
	} else if param.UrlType == 3 { //二维码
		result["qrcodeUrl"] = genQrcodeUrl(ctx, longUrl)
	}
	return result, nil
}

func PageListDownloadTask(ctx *gin.Context, pn, ps int) (*dv.Pager, error) {
	var (
		total int64
		list  []models.ZeroActivityDownload
	)
	_db := helpers.MysqlClient.WithContext(ctx).Model(&models.ZeroActivityDownload{}).Where("deleted=0")

	if err := _db.Count(&total).Error; err != nil {
		return nil, err
	}
	if total == 0 {
		return dv.WrapPager(total, dv.EmptyList{}), nil
	}
	_db.Order("id desc").Scopes(models.NormalPaginate(&models.NormalPage{No: pn, Size: ps})).Find(&list)

	return dv.WrapPager(total, list), nil
}

func DelDownloadTask(ctx *gin.Context, id int64) error {
	return helpers.MysqlClient.WithContext(ctx).Model(&models.ZeroActivityDownload{}).
		Where("id=? and status !=2", id).Update("deleted", 1).Error
}

func AddDownloadTask(ctx *gin.Context, param data2.ActChannelParam) error {
	var act models.ZeroActivity
	helpers.MysqlClient.WithContext(ctx).Where("id=?", param.ActId).Take(&act)
	if act.ID == 0 {
		return components.ErrorParamInvalid
	}

	extStr, _ := jsoniter.MarshalToString(param)
	timeStr := time.Now().Format("2006-01-02 15_04_05")
	fileName := fmt.Sprintf("%s_短链/二维码下载_%s", act.ActyName, timeStr)
	task := models.ZeroActivityDownload{
		ActId:      int64(act.ID),
		FileName:   fileName,
		Status:     1,
		CreateAt:   middleware.GetCtxUsername(ctx),
		CreateTime: time.Now().Unix(),
		Ext:        extStr,
	}
	return helpers.MysqlClient.WithContext(ctx).Create(&task).Error
}

// 分流任务ID 检查对应的活动渠道是否一致
func checkFlowTaskActChannel(ctx *gin.Context, taskIds map[int]string) (ret bool, err error) {
	if len(taskIds) == 1 {
		return true, nil
	}
	taskIdToChannels := make(map[int][]int)
	chGlimit := make(chan struct{}, len(taskIds))
	var wg sync.WaitGroup
	hasErr := false
	for taskId := range taskIds {
		wg.Add(1)
		chGlimit <- struct{}{}
		if _, ok := taskIdToChannels[taskId]; !ok {
			taskIdToChannels[taskId] = make([]int, 0)
		}
		go func(taskId int) {
			defer wg.Done()
			defer func() {
				<-chGlimit
			}()
			channels, err := getAllListActChannel(ctx, taskId)
			if err != nil {
				hasErr = true
				zlog.Warnf(ctx, "getAllListActChannel taskId:%d error: %s", taskId, err.Error())
				return
			}
			for _, channel := range channels {
				taskIdToChannels[taskId] = append(taskIdToChannels[taskId], channel.ChannelID)
			}
		}(taskId)
	}
	wg.Wait()
	close(chGlimit)
	if hasErr {
		return false, fmt.Errorf("获取群控任务活动渠道列表失败，请稍后重试。")
	}
	// 检查渠道是否一致
	i := 0
	baseChannels := make([]int, 0)
	for _, ints := range taskIdToChannels {
		if i == 0 {
			baseChannels = ints
		} else {
			if !utils.SlicesEqualIgnoreOrder(baseChannels, ints) {
				return false, fmt.Errorf("分流任务ID，活动渠道不一致")
			}
		}
		i++
	}
	return true, nil
}

// 获取群控任务下所有活动渠道
func getAllListActChannel(ctx *gin.Context, taskID int) (dataList []api_wxqk.ChannelListItem, err error) {
	var (
		param = api_wxqk.ListChannelParam{
			PageNo:   1,
			PageSize: 500,
			H5TaskId: taskID,
		}
	)
	for {
		resp, err := api_wxqk.ListChannelInfo(ctx, param)
		if err != nil {
			zlog.Warnf(ctx, "0元活动查询h5渠道异常：%s", err.Error())
			return nil, err
		}
		if len(resp.List) > 0 {
			dataList = append(dataList, resp.List...)
		}
		if resp.Meta.Total <= param.PageNo*param.PageSize {
			break
		}
		param.PageNo++
	}
	return
}
