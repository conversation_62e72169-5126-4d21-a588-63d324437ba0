package afx

import (
	"fmt"
	constant "git.zuoyebang.cc/ad/gocommons/const"
	"git.zuoyebang.cc/ad/gocommons/utils"
	"github.com/pkg/errors"
	"go-hetu/components"
	"go-hetu/data"
	"go-hetu/helpers"
	"go-hetu/models"
	"gorm.io/gorm"
	"strings"
	"sync"
	"time"

	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

// 推广产品一级对应关系 https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=475822353
func (s service) ScriptInitPromote(ctx *gin.Context) (err error) {
	xiaoxue := []int{11, 12, 13, 14, 15, 16}
	gaozhong := []int{5, 6, 7}
	chuzhong := []int{2, 3, 4}
	// allGrade := append(append(xiaoxue, chuzhong...), gaozhong...) //

	subjectResp, err := s.qdmisAPIService.GetSkuPoolParamList(ctx)
	if err != nil {
		return
	}
	if subjectResp == nil {
		return components.ErrorAPI.Sprintf("get subject error")
	}
	allSubject := make([]int, 0)
	for _, subject := range subjectResp.Subject {
		allSubject = append(allSubject, subject.Value)
	}
	allGrade := make([]int, 0)
	for _, grade := range subjectResp.Grade {
		allGrade = append(allGrade, grade.Value)
	}

	jinxiao := []int{40, 41, 34, 79}
	biancheng := []int{40, 41}

	promoteResp, err := s.qdmisAPIService.GetPromoteList(ctx)
	if err != nil {
		return components.ErrorAPI.Sprintf("get promotelv1 erro")
	}
	insertData := make([]*tfmodels.TblAFXPromoteRelation, 0)
	for _, v := range promoteResp.Promote {
		promoteLv1 := v.Key
		category := 1
		insertItem := &tfmodels.TblAFXPromoteRelation{
			PromoteLv1: int(promoteLv1),
			Category:   category,
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		}
		switch v.Key {
		case 449: // 小学
			for _, vv := range xiaoxue {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 450: // 初中
			for _, vv := range chuzhong {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 451: // 高中
			for _, vv := range gaozhong {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 453: // 小鹿进校
			for _, vv := range jinxiao {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 452: // 小鹿编程
			for _, vv := range biancheng {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 470: // 小鹿写字
			insertItem.Category = 2
			insertItem.CategoryValue = 1
			insertData = append(insertData, insertItem)
		case 469: // 小鹿体育
			insertItem.Category = 2
			insertItem.CategoryValue = 1
			insertData = append(insertData, insertItem)
		case 454: // GX
			for _, vv := range allGrade {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
			for _, vv := range allSubject {
				insertItem.Category = 2
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 7, 8, 9: // 0转正
			for _, vv := range allGrade {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		case 6: // 0转正低价
			for _, vv := range allGrade {
				insertItem.CategoryValue = vv
				insertData = append(insertData, insertItem)
			}
		}
	}
	return s.afxPromoteRepo.BatchInsert(ctx, insertData)
}

type FlushStruct []struct {
	InstId    int64  `json:"instId"`
	StartTime int64  `json:"startTime"`
	Cadre     string `json:"cadre"`
}

func (s service) ScriptInitUserRelation(ctx *gin.Context) (err error) {

	var flushStruct FlushStruct
	_ = jsoniter.UnmarshalFromString(flushJsonData, &flushStruct)

	insertData := make([]*tfmodels.TblAFXUserRelation, 0)

	promoteCond := models.NewAfxPromoteCond()
	promoteList, err := s.afxPromoteRepo.SelectByParams(ctx, promoteCond)
	if err != nil {
		return components.ErrorParamFriendly.Sprintf("get tblAFXPromoteRelation failed")
	}

	// 获取所有代理商机构
	instList, err := s.batchGetInstData(ctx)
	if err != nil {
		return
	}

	promoteMap := make(map[int]map[int]map[int]struct{}) // key1: promote_id, key2: category, key3: category_value
	for _, promoteItem := range promoteList {
		if _, ok := promoteMap[promoteItem.PromoteLv1]; !ok {
			promoteMap[promoteItem.PromoteLv1] = make(map[int]map[int]struct{})
		}
		if _, ok := promoteMap[promoteItem.PromoteLv1][promoteItem.Category]; !ok {
			promoteMap[promoteItem.PromoteLv1][promoteItem.Category] = make(map[int]struct{})
		}
		if _, ok := promoteMap[promoteItem.PromoteLv1][promoteItem.Category][promoteItem.CategoryValue]; !ok {
			promoteMap[promoteItem.PromoteLv1][promoteItem.Category][promoteItem.CategoryValue] = struct{}{}
		}
	}

	appPromoteMap := map[int]map[int]struct{}{ // key1: app_id, key2: promote_id
		1: {
			499: struct{}{},
			450: struct{}{},
		},
		2: {
			499: struct{}{},
			450: struct{}{},
		},
		3: {
			499: struct{}{},
			450: struct{}{},
		},
		11: {
			6: struct{}{},
			7: struct{}{},
			8: struct{}{},
			9: struct{}{},
		},
		13: {
			6: struct{}{},
			7: struct{}{},
			8: struct{}{},
			9: struct{}{},
		},
		17: {
			451: struct{}{},
		},
	}

	instCadreMap := make(map[int64]string)
	instCreateMap := make(map[int64]int64)
	for _, flushItem := range flushStruct {
		if _, ok := instCadreMap[flushItem.InstId]; !ok {
			instCadreMap[flushItem.InstId] = flushItem.Cadre // 归属人
		}
		if _, ok := instCreateMap[flushItem.InstId]; !ok {
			instCreateMap[flushItem.InstId] = flushItem.StartTime // 报备通过时间
		}
	}

	instAppMap := make(map[int64]int64)
	instPromoteMap := make(map[int64]map[int]struct{})
	for _, instItem := range instList {
		if instItem.Status > 1 {
			continue
		}
		// 本次数据仅处理 1887条
		if _, ok := instCadreMap[instItem.ID]; !ok {
			continue
		}
		// if _, ok := instCadreMap[instItem.ID]; !ok {
		// 	instCadreMap[instItem.ID] = instItem.Cadre // 归属人
		// }
		if _, ok := instAppMap[instItem.ID]; !ok {
			instAppMap[instItem.ID] = instItem.AppID
		}
		// if _, ok := instCreateMap[instItem.ID]; !ok {
		// 	instCreateMap[instItem.ID] = instItem.CreateTime // 报备通过时间
		// }
		promoteMap, ok := appPromoteMap[int(instItem.AppID)]
		if !ok {
			zlog.Warnf(ctx, "tblAFXInst.app_id not match, app_id = %v", instItem.AppID)
		} else {
			if _, _ok := instPromoteMap[instItem.ID]; !_ok {
				instPromoteMap[instItem.ID] = make(map[int]struct{})
			}
			for promote := range promoteMap {
				if _, _ok := instPromoteMap[instItem.ID][promote]; !_ok {
					instPromoteMap[instItem.ID][promote] = struct{}{}
				}
			}
		}
	}

	for instID, promoteValue := range instPromoteMap {
		for promoteKey := range promoteValue {
			for category, promoteValue := range promoteMap[promoteKey] {
				for categoryValue := range promoteValue {
					appID := instAppMap[instID]
					cadre := instCadreMap[instID]
					startTime := instCreateMap[instID]
					insertItem := &tfmodels.TblAFXUserRelation{
						InstID:        instID,
						AppID:         appID,
						PromoteLv1:    promoteKey,
						RelationType:  category,
						RelationValue: categoryValue,
						Cadre:         cadre,
						Status:        1,
						StartTime:     startTime,
						EndTime:       0,
						CreateTime:    time.Now().Unix(),
						UpdateTime:    time.Now().Unix(),
					}
					insertData = append(insertData, insertItem)

				}
			}
		}

	}

	return s.afxUserRelationRepo.BatchInsert(ctx, insertData)
}

// 使用sql进行数据清洗
func (s service) ScriptUpdateScodeInst(ctx *gin.Context) (err error) {
	return
}

func (s service) batchGetInstData(ctx *gin.Context) (list []tfmodels.TblAFXInst, err error) {
	pageSize := 500

	instCond := models.NewAfxInstCond()
	instCond.WithPageOpt(1, pageSize)
	instCond.FxInstIDGtZero()
	total, list, err := s.afxInstRepo.SelectWithTotalByParams(ctx, instCond)

	if total <= int64(pageSize) {
		return
	}

	pageCount := total / int64(pageSize)
	if total%int64(pageSize) != 0 {
		pageCount++
	}

	chanData := make(chan []tfmodels.TblAFXInst, pageCount)
	chanErr := make(chan error, pageCount)
	chanPage := make(chan int, pageCount)

	var wg sync.WaitGroup
	wg.Add(int(pageCount) - 1)
	for i := 2; i <= int(pageCount); i++ {
		chanPage <- i
		go func(ctx *gin.Context) {
			defer func() {
				wg.Done()
				if r := recover(); r != nil {
					zlog.Errorf(ctx, "goroutine batchGetInstList failed, [err:%v]", r)
				}
			}()
			subInstCond := models.NewAfxInstCond()
			subInstCond.WithPageOpt(<-chanPage, pageSize)
			subInstCond.FxInstIDGtZero()
			_, subList, _err := s.afxInstRepo.SelectWithTotalByParams(ctx, subInstCond)
			chanData <- subList
			if _err != nil {
				chanErr <- _err
			}
			time.Sleep(1 * time.Second)
		}(ctx.Copy())
	}
	wg.Wait()
	close(chanData)
	close(chanErr)
	close(chanPage)

	for err = range chanErr {
		if err != nil {
			return nil, err
		}
	}

	for item := range chanData {
		list = append(list, item...)
	}
	return
}

// 根据产品提供的 inst_id + app_id 迁移到新的 app_id 下
// 同时迁移原 inst_id 下 用户信息，需要注意分销码信息
func (s service) ScriptCopyInstToNewApp(ctx *gin.Context) (err error) {
	// copyDataJson = `[
	// 	{"fxInstId":8499,"instId":11226,"appId":1,"targetAppId":8,"promoteLv1":449,"cadre":"zhangan01","startTime":1698375877,"endTime":0},
	// 	{"fxInstId":8499,"instId":11226,"appId":1,"targetAppId":8,"promoteLv1":450,"cadre":"zhangan01","startTime":1698375877,"endTime":0}
	// ]`
	var copyData CopyDataStruct
	_ = jsoniter.UnmarshalFromString(copyDataJson, &copyData)

	for _, item := range copyData {
		_err := s.handleCopyData(ctx, item.FxInstId, item.InstId, item.AppId, item.TargetAppId, item.PromoteLv1, item.Cadre, item.StartTime, item.EndTime)
		if _err != nil {
			zlog.Warnf(ctx, "copyInst failed, copyData=[%v]", item)
			continue
		}
	}
	return
}

func (s service) ScriptFixUserRelation(ctx *gin.Context, limit int) (err error) {
	// copyDataJson = `[
	// 	{"fxInstId":8499,"instId":11226,"appId":1,"targetAppId":8,"promoteLv1":449,"cadre":"zhangan01","startTime":1698375877,"endTime":0},
	// 	{"fxInstId":8499,"instId":11226,"appId":1,"targetAppId":8,"promoteLv1":450,"cadre":"zhangan01","startTime":1698375877,"endTime":0}
	// ]`
	var copyData CopyDataStruct
	_ = jsoniter.UnmarshalFromString(copyDataJson, &copyData)
	var cnt = 0
	for _, item := range copyData {
		if limit > 0 && cnt >= limit {
			break
		}
		cnt++
		_err := s.handleCopyRelationData(ctx, item.FxInstId, item.AppId, item.TargetAppId, item.PromoteLv1, item.Cadre, item.StartTime, item.EndTime)
		if _err != nil {
			zlog.Warnf(ctx, "copyInst failed, copyData=[%v]", item)
			continue
		}
	}
	return
}

func (s service) ScriptFixInstData(ctx *gin.Context, startTime int, endTime int, limit int) (err error) {
	/*
		1. 查询脚本生成的代理商机构账号
		2. instListMap map[instID_appID][]tblAFXInst{}
		3. 处理查到两条代理商机构的数据：两条数据A， B
			3.1 删除代理商B，将B下的账号改为A
			3.2 delete from tblAFXInst where id = B.id limit 1;
			3.3 delete from tblAFXUser where inst_id = B.id;
			3.4 delete from tblAFXSCode where inst_id = B.id;
			3.5 update tblAFXUserRelation set inst_id = A.id where inst_id = B.id
	*/
	var instList []tfmodels.TblAFXInst
	err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXInst{}).
		Where("create_time >= ? and create_time <= ?", startTime, endTime).Find(&instList).Error
	if err != nil {
		return
	}
	zlog.Infof(ctx, "get tblAFXInst success, lent(instList)=[%v]", len(instList))
	instListMap := make(map[string][]tfmodels.TblAFXInst)
	for _, item := range instList {
		key := fmt.Sprintf("%v_%v", item.FxInstID, item.AppID)
		if _, ok := instListMap[key]; !ok {
			instListMap[key] = make([]tfmodels.TblAFXInst, 0)
		}
		instListMap[key] = append(instListMap[key], item)
	}
	var cnt int
	for _, sameInsts := range instListMap {
		if len(sameInsts) <= 1 {
			continue
		}
		// 3.1 删除代理商B，将B下的账号改为A
		instA := sameInsts[0]
		for _, instB := range sameInsts[1:] {
			if limit > 0 && cnt >= limit {
				break
			}
			cnt++
			err = helpers.MysqlClient.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
				// 3.2 delete from tblAFXInst where id = B.id limit 1;
				var deleteInstList []tfmodels.TblAFXInst
				err = tx.WithContext(ctx).Model(&tfmodels.TblAFXInst{}).
					Where("id = ?", instB.ID).Delete(&deleteInstList).Error
				if err != nil {
					zlog.Warnf(ctx, "delete tblAFXInst failed, inst_id=[%v], err=[%v]", instB.ID, err)
					return err
				}
				zlog.Infof(ctx, "delete tblAFXInst success, instInfo=[%+v]", deleteInstList)
				// 3.3 delete from tblAFXUser where inst_id = B.id;
				var deleteUserList []tfmodels.TblAFXUser
				err = tx.WithContext(ctx).Model(&tfmodels.TblAFXUser{}).
					Where("inst_id = ?", instB.ID).Delete(&deleteUserList).Error
				if err != nil {
					zlog.Warnf(ctx, "delete tblAFXUser failed, inst_id=[%v], err=[%v]", instB.ID, err)
					return err
				}
				zlog.Infof(ctx, "delete tblAFXUser success, instInfo=[%+v]", deleteUserList)
				// 3.4 delete from tblAFXSCode where inst_id = B.id;
				deleteScodeList := make([]tfmodels.TblAFXSCode, 0)
				err = tx.WithContext(ctx).Model(&tfmodels.TblAFXSCode{}).
					Where("inst_id = ?", instB.ID).Delete(&deleteScodeList).Error
				if err != nil {
					zlog.Warnf(ctx, "delete tblAFXSCode failed, inst_id=[%v], err=[%v]", instB.ID, err)
					return err
				}
				zlog.Infof(ctx, "delete tblAFXSCode success, codeInfo=[%+v]", deleteScodeList)
				// 3.5 update tblAFXUserRelation set inst_id = A.id where inst_id = B.id
				var userList []tfmodels.TblAFXUserRelation
				err = tx.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
					Where("inst_id = ?", instB.ID).Find(&userList).Error
				// 唯一键：UNIQUE KEY `uk_relation_cadre` (`inst_id`,`app_id`,`promote_lv1`,`relation_type`,`relation_value`,`cadre`,`start_time`)
				// 如果唯一键存在，则删除。否则更新
				for i := range userList {
					var exist int64
					err = tx.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
						Where("inst_id = ?", instA.ID).
						Where("app_id = ?", userList[i].AppID).
						Where("promote_lv1 = ?", userList[i].PromoteLv1).
						Where("relation_type = ?", userList[i].RelationType).
						Where("relation_value = ?", userList[i].RelationValue).
						Where("cadre = ?", userList[i].Cadre).
						Where("start_time = ?", userList[i].StartTime).
						Count(&exist).Error
					if err != nil {
						zlog.Warnf(ctx, "count tblAFXUserRelation failed, inst_id=[%v], err=[%v]", userList[i].InstID, err)
						return err
					}
					if exist > 0 {
						//relation关系已经存在，则直接删除
						err = tx.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
							Where("id = ?", userList[i].ID).
							Delete(&tfmodels.TblAFXUserRelation{}).Error
						if err != nil {
							zlog.Warnf(ctx, "delete tblAFXUserRelation failed, inst_id=[%v], err=[%v]", userList[i].InstID, err)
							return err
						}
					} else {
						// 不存在则移植到新instID下
						err = tx.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
							Where("id = ?", userList[i].ID).
							Updates(map[string]interface{}{
								"inst_id": instA.ID,
							}).Error
						if err != nil {
							zlog.Warnf(ctx, "update tblAFXUserRelation failed, inst_id=[%v], err=[%v]", userList[i].InstID, err)
							return err
						}
					}
				}
				return nil
			})
			if err != nil {
				zlog.Warnf(ctx, "transaction failed: instInfo=[%+v], err=[%v]", instB, err)
				continue
			}
		}
	}
	return nil
}

type CopyDataStruct []struct {
	FxInstId    int64  `json:"fxInstId"`
	InstId      int64  `json:"instId"`
	AppId       int64  `json:"appId"`
	TargetAppId int64  `json:"targetAppId"`
	PromoteLv1  int    `json:"promoteLv1"`
	Cadre       string `json:"cadre"`
	StartTime   int64  `json:"startTime"`
	EndTime     int64  `json:"endTime"`
}

type RoleStruct struct {
	CurId int64
	Pid   int64
	Data  []*tfmodels.TblAFXUser
}

func (s service) handleCopyData(
	ctx *gin.Context,
	fxInstId, instId, appId, targetAppId int64,
	promoteLv1 int,
	cadre string,
	startTime, endTime int64) (err error) {

	promoteRelation := map[int][]int{
		449: {1},      // 小学
		450: {20},     // 初中
		451: {30},     // 高中
		452: {40, 41}, // 小鹿编程有价
		453: {40, 41, 34, 79},
		469: {79},                                                             // 小鹿体育
		470: {34},                                                             // 小鹿写字
		454: {1, 9, 5, 3, 46, 16, 47, 54, 4, 33, 57, 55, 2, 44, 8, 7, 12, 72}, // GX
	}
	promoteRelationType := map[int]int{
		449: 1,
		450: 1,
		451: 1,
		452: 2,
		453: 2,
		469: 2,
		470: 2,
		454: 2,
	}

	instCond := models.NewAfxInstCond()
	instCond.WithFxInstID(fxInstId)
	instCond.WithAppID(targetAppId)
	fxInstAppList, err := s.afxInstRepo.SelectByParams(ctx, instCond)
	if err != nil {
		return
	}
	if len(fxInstAppList) > 0 {
		extraRelation := make([]*tfmodels.TblAFXUserRelation, 0)
		for _, relationValue := range promoteRelation[promoteLv1] {
			extraItem := &tfmodels.TblAFXUserRelation{
				//ID:            fxInstAppList[0].ID,
				InstID:        fxInstAppList[0].ID,
				AppID:         appId,
				PromoteLv1:    promoteLv1,
				RelationType:  promoteRelationType[promoteLv1],
				RelationValue: relationValue,
				Cadre:         cadre,
				Status:        1,
				StartTime:     startTime,
				EndTime:       endTime,
				CreateTime:    time.Now().Unix(),
				UpdateTime:    time.Now().Unix(),
			}
			extraRelation = append(extraRelation, extraItem)
		}
		_err := s.afxUserRelationRepo.BatchInsert(ctx, extraRelation)
		if _err != nil {
			zlog.Errorf(ctx, "[hetu]transaction insert tblAFXUserRelation failed, err=[%+v], insertData=[%+v]",
				_err, extraRelation)
			return _err
		}
		zlog.Infof(ctx, "[hetu-script] get tblAFXInst not empty, inst_id=[%v], fx_inst_id=[%v], app_id=[%v]",
			fxInstAppList[0].ID, fxInstId, appId)
		return
	}

	// 查询当前代理商机构数据
	instCond = models.NewAfxInstCond()
	instCond.WithInstID(instId)
	instCond.WithAppID(appId)
	instList, err := s.afxInstRepo.SelectByParams(ctx, instCond)
	if err != nil {
		return
	}
	if len(instList) == 0 {
		zlog.Infof(ctx, "[hetu-script] get tblAFXInst empty, inst_id=[%v], app_id=[%v]",
			instId, appId)
		return
	}
	instItem := instList[0]

	newInstData := &tfmodels.TblAFXInst{
		AppID:         targetAppId,
		Province:      instItem.Province,
		City:          instItem.City,
		Area:          instItem.Area,
		Address:       instItem.Address,
		Name:          instItem.Name,
		Type:          instItem.Type,
		Contacts:      instItem.Contacts,
		ShopCode:      instItem.ShopCode,
		Email:         instItem.Email,
		Mobile:        instItem.Mobile,
		SaleInfo:      instItem.SaleInfo,
		SaleChannel:   instItem.SaleChannel,
		AgentCode:     instItem.AgentCode,
		CreateUID:     instItem.CreateUID,
		CreateID:      instItem.CreateID,
		Cadre:         cadre,
		Status:        instItem.Status,
		CreateTime:    time.Now().Unix(),
		UpdateTime:    time.Now().Unix(),
		ExtData:       instItem.ExtData,
		State:         instItem.State,
		FxInstID:      instItem.FxInstID,
		ZtfCanChannel: instItem.ZtfCanChannel,
	}

	// 查询当前代理商机构的用户数据
	userCond := models.NewAfxUserCond()
	userCond.WithAppId(appId)
	userCond.WithInstId(instId)
	userList, err := s.afxUserRepo.SelectByParams(ctx, userCond)
	if err != nil {
		return
	}
	if len(userList) == 0 {
		return
	}
	// newRoleLv1 := make(map[int][]*tfmodels.TblAFXUser, 0)
	newRoleLv1 := &RoleStruct{}
	newRoleLv2 := make(map[int]*RoleStruct)
	newRoleLv3 := make(map[int]*RoleStruct)
	// newRoleLv2 := make(map[int][]*tfmodels.TblAFXUser, 0)
	// newRoleLv3 := make(map[int][]*tfmodels.TblAFXUser, 0)
	shareCodeData := make([]*tfmodels.TblAFXSCode, 0)
	shareCodeMap := make(map[int64]string)
	for _, item := range userList {
		userItem := &tfmodels.TblAFXUser{
			AppID:           targetAppId,
			UID:             item.UID,
			Nickname:        item.Nickname,
			Cadre:           cadre,
			ModelType:       item.ModelType,
			CooperationType: item.CooperationType,
			Email:           item.Email,
			RoleID:          item.RoleID,
			Status:          item.Status,
			Deleted:         item.Deleted,
			CreateTime:      time.Now().Unix(),
			UpdateTime:      time.Now().Unix(),
			ExtData:         item.ExtData,
		}
		if _, ok := shareCodeMap[item.UID]; !ok {
			shareCode, _ := s.makeShareCode(ctx, 0, 0, 0, item.UID)
			shareCodeMap[item.UID] = shareCode
			scodeData := &tfmodels.TblAFXSCode{
				RoleID:     item.RoleID,
				AppID:      targetAppId,
				UID:        item.UID,
				ShareCode:  shareCode,
				CreateTime: time.Now().Unix(),
				UpdateTime: time.Now().Unix(),
				ExtData:    item.ExtData,
			}
			shareCodeData = append(shareCodeData, scodeData)
		}
		if item.RoleID == 11 {
			newRoleLv1.CurId = item.ID
			newRoleLv1.Pid = item.Pid
			newRoleLv1.Data = append(newRoleLv1.Data, userItem)

			// if _, ok := newRoleLv1[int(item.ID)]; !ok {
			// 	newRoleLv1[int(item.ID)] = make([]*tfmodels.TblAFXUser, 0)
			// }
			// newRoleLv1[int(item.ID)] = append(newRoleLv1[int(item.ID)], userItem)
		}
		if item.RoleID == 12 {
			if _, ok := newRoleLv2[int(item.Pid)]; !ok {
				newRoleLv2[int(item.Pid)] = &RoleStruct{
					CurId: item.ID,
					Pid:   item.Pid,
					Data:  make([]*tfmodels.TblAFXUser, 0),
				}
			}
			newRoleLv2[int(item.Pid)].Data = append(newRoleLv2[int(item.Pid)].Data, userItem)
		}
		if item.RoleID == 13 {
			if _, ok := newRoleLv3[int(item.Pid)]; !ok {
				newRoleLv3[int(item.Pid)] = &RoleStruct{
					CurId: item.ID,
					Pid:   item.Pid,
					Data:  make([]*tfmodels.TblAFXUser, 0),
				}
			}
			newRoleLv3[int(item.Pid)].Data = append(newRoleLv3[int(item.Pid)].Data, userItem)
		}
	}

	userRelationData := make([]*tfmodels.TblAFXUserRelation, 0)
	for _, relationValue := range promoteRelation[promoteLv1] {
		relationType := promoteRelationType[promoteLv1]
		userRelationItem := &tfmodels.TblAFXUserRelation{
			AppID:         targetAppId,
			PromoteLv1:    promoteLv1,
			RelationType:  relationType,
			RelationValue: relationValue,
			Cadre:         cadre,
			Status:        1,
			StartTime:     startTime,
			EndTime:       endTime,
			CreateTime:    time.Now().Unix(),
			UpdateTime:    time.Now().Unix(),
		}
		userRelationData = append(userRelationData, userRelationItem)
	}
	// 新增数据
	return s.transactionScriptSave(ctx, newInstData, newRoleLv1, newRoleLv2, newRoleLv3, shareCodeData, userRelationData)
}

func (s service) handleCopyRelationData(
	ctx *gin.Context,
	fxInstId, appId, targetAppId int64,
	promoteLv1 int,
	cadre string,
	startTime, endTime int64) (err error) {

	promoteRelation := map[int][]int{
		449: {1},      // 小学
		450: {20},     // 初中
		451: {30},     // 高中
		452: {40, 41}, // 小鹿编程有价
		453: {40, 41, 34, 79},
		469: {79},                                                             // 小鹿体育
		470: {34},                                                             // 小鹿写字
		454: {1, 9, 5, 3, 46, 16, 47, 54, 4, 33, 57, 55, 2, 44, 8, 7, 12, 72}, // GX
	}
	promoteRelationType := map[int]int{
		449: 1,
		450: 1,
		451: 1,
		452: 2,
		453: 2,
		469: 2,
		470: 2,
		454: 2,
	}

	instCond := models.NewAfxInstCond()
	instCond.WithFxInstID(fxInstId)
	instCond.WithAppID(targetAppId)

	fxInstAppList, err := s.afxInstRepo.SelectByParams(ctx, instCond)
	if err != nil {
		return
	}
	if len(fxInstAppList) > 0 {
		extraRelation := make([]*tfmodels.TblAFXUserRelation, 0)
		for _, relationValue := range promoteRelation[promoteLv1] {
			extraItem := &tfmodels.TblAFXUserRelation{
				//ID:            fxInstAppList[0].ID,
				InstID:        fxInstAppList[0].ID,
				AppID:         appId,
				PromoteLv1:    promoteLv1,
				RelationType:  promoteRelationType[promoteLv1],
				RelationValue: relationValue,
				Cadre:         cadre,
				Status:        1,
				StartTime:     startTime,
				EndTime:       endTime,
				CreateTime:    time.Now().Unix(),
				UpdateTime:    time.Now().Unix(),
			}
			extraRelation = append(extraRelation, extraItem)
		}
		_err := s.afxUserRelationRepo.BatchInsert(ctx, extraRelation)
		if _err != nil {
			zlog.Warnf(ctx, "[hetu]transaction insert tblAFXUserRelation failed, err=[%+v], insertData=[%+v]",
				_err, extraRelation)
			return _err
		}
		zlog.Infof(ctx, "[hetu-script] get tblAFXInst not empty, inst_id=[%v], fx_inst_id=[%v], app_id=[%v]",
			fxInstAppList[0].ID, fxInstId, appId)
		return
	}
	return
}

func (s service) transactionScriptSave(
	ctx *gin.Context,
	newInstData *tfmodels.TblAFXInst,
	roleLv1Data *RoleStruct,
	roleLv2Data map[int]*RoleStruct,
	roleLv3Data map[int]*RoleStruct,
	shareCodeData []*tfmodels.TblAFXSCode,
	userRelation []*tfmodels.TblAFXUserRelation,
) (err error) {
	dbc := s.afxUserRepo.GetDB(ctx)
	err = dbc.Transactional(ctx, func(ctx *gin.Context) error {
		// 写入 tblAfxInst
		_err := s.afxInstRepo.Insert(ctx, newInstData)
		if _err != nil {
			zlog.Errorf(ctx, "[hetu]transaction insert tblAFXInst failed, err=[%+v], insertData=[%+v]",
				_err, newInstData)
			return _err
		}
		for _, item := range roleLv1Data.Data {
			item.InstID = newInstData.ID
		}
		_err = s.afxUserRepo.BatchInsert(ctx, roleLv1Data.Data)
		if _err != nil {
			zlog.Errorf(ctx, "[hetu]transaction insert tblAFXUser failed, err=[%+v], insertData=[%+v]",
				_err, roleLv1Data)
			return _err
		}
		for oldPid, roleData := range roleLv2Data {
			if roleLv1Data.CurId == int64(oldPid) {
				for _, item := range roleData.Data {
					item.Pid = roleLv1Data.Data[0].ID
					item.Relation = fmt.Sprintf("|%d|", item.Pid)
					item.AID = item.Pid
					item.InstID = newInstData.ID
				}
				_err = s.afxUserRepo.BatchInsert(ctx, roleData.Data)
				if _err != nil {
					zlog.Errorf(ctx, "[hetu]transaction insert tblAFXUser failed, err=[%+v], insertData=[%+v]",
						_err, roleLv2Data)
					return _err
				}
			}
		}
		for oldPid, roleData := range roleLv3Data {
			for _, roleLv2Item := range roleLv2Data {
				if roleLv2Item.CurId == int64(oldPid) {
					roleLv3Pid := roleLv2Item.Data[0].ID
					roleLv3Relation := fmt.Sprintf("%s%d|", roleLv2Item.Data[0].Relation, roleLv3Pid)
					for _, item := range roleData.Data {
						item.Pid = roleLv3Pid
						item.Relation = roleLv3Relation
						item.AID = roleLv3Pid
						item.InstID = newInstData.ID
					}
					_err = s.afxUserRepo.BatchInsert(ctx, roleData.Data)
					if _err != nil {
						zlog.Errorf(ctx, "[hetu]transaction insert tblAFXUser failed, err=[%+v], insertData=[%+v]",
							_err, roleLv3Data)
						return _err
					}
				}
			}
		}

		for _, item := range shareCodeData {
			item.InstID = newInstData.ID
		}
		_err = s.afxSCodeRepo.BatchInsert(ctx, shareCodeData)
		if _err != nil {
			zlog.Errorf(ctx, "[hetu]transaction insert tblAFXSCode failed, err=[%+v], insertData=[%+v]",
				_err, shareCodeData)
			return _err
		}

		for _, item := range userRelation {
			item.InstID = newInstData.ID
		}
		_err = s.afxUserRelationRepo.BatchInsert(ctx, userRelation)
		if _err != nil {
			zlog.Errorf(ctx, "[hetu]transaction insert tblAFXUserRelation failed, err=[%+v], insertData=[%+v]",
				_err, userRelation)
			return _err
		}

		return nil
	})

	if err != nil {
		return components.ErrorDbInsert.Sprintf("事务执行失败")
	}
	return
}

func (s service) ScriptFixOrderCadre(ctx *gin.Context, methodType string, instIDs []int64, startTime, endTime int64) (err error) {
	//1. 从unionOrder表中查询指定日期的orderList
	if startTime == 0 || endTime == 0 {
		startTime = time.Date(2023, 12, 10, 0, 0, 0, 0, time.Local).Unix()
		endTime = time.Date(2024, 01, 13, 0, 0, 0, 0, time.Local).Unix()
	}
	if len(instIDs) == 0 {
		instIDs = FixCadreInstID
	}
	zlog.Infof(ctx, "[ScriptFixOrderCadre] instIDs=[%v], startTime=[%v], endTime=[%v]", instIDs, startTime, endTime)

	// 按照河图ID更新归属人
	for i := range instIDs {
		err = s.RerunInstOrder(ctx, methodType, instIDs[i], startTime, endTime)
		if err != nil {
			zlog.Warnf(ctx, "[ScriptFixOrderCadre] RerunInstOrder failed: instID=[%v], err=[%v]", instIDs[i], err)
			continue
		}
	}
	return
}

// 刷新机构下代理商订单归属人
func (s service) RerunInstOrder(ctx *gin.Context, methodType string, instID int64, startTime, endTime int64) (err error) {
	if instID <= 0 {
		zlog.Warnf(ctx, "[RerunInstOrder] instID empty")
		return
	}
	agentMap := map[int64]struct{}{}
	var page, pageSize int
	pageSize = 100
	for page = 0; ; page++ {
		var subAgentList []tfmodels.TblAFXUser
		err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUser{}).
			Select("id", "cadre").
			Where("inst_id = ?", instID).Order("id asc").
			Offset(page * pageSize).Limit(pageSize).
			Find(&subAgentList).Error
		if err != nil {
			zlog.Warnf(ctx, "[RerunInstOrder] Get agentIDs failed: instID=[%v] err=[%v]", instID, err)
			return err
		}

		for i := range subAgentList {
			if _, ok := agentMap[subAgentList[i].ID]; ok {
				zlog.Infof(ctx, "[RerunInstOrder] agentID=[%v] already run", subAgentList[i].ID)
				continue
			}
			err = s.RerunAgentOrder(ctx, methodType, subAgentList[i].ID, startTime, endTime, subAgentList[i].Cadre)
			if err != nil {
				zlog.Warnf(ctx, "[RerunInstOrder] RerunAgentOrder failed: agentID=[%v] err=[%v]", subAgentList[i].ID, err)
				continue
			}
			agentMap[subAgentList[i].ID] = struct{}{}
		}

		if len(subAgentList) == 0 {
			break
		}
	}
	return nil
}

type AFXUnionOrderInfo struct {
	ID        int64  `gorm:"column:id" json:"id"`
	OrderID   int64  `gorm:"column:order_id" json:"orderId"`
	OrderTime int    `gorm:"column:order_time" json:"orderTime"`
	Cadre     string `gorm:"column:cadre" json:"cadre"`
	Lastfrom  string `gorm:"column:lastfrom" json:"lastfrom"`
	AgentID   int64  `gorm:"column:agent_id" json:"agentId"`
	AppID     int    `gorm:"column:app_id" json:"appId"`
	UserID    int64  `gorm:"column:user_id" json:"userId"`
}

// 刷新代理商订单归属人
func (s service) RerunAgentOrder(ctx *gin.Context, methodType string, agentID, startTime, endTime int64, defaultName string) (err error) {
	var orderNumber int
	// 分页获取订单
	page := 0
	pageSize := 100
	for page = 0; ; page++ {
		var orderList []AFXUnionOrderInfo
		err = helpers.MysqlClient.WithContext(ctx).Table("tblAFXUnionOrder").
			Select("id", "order_id", "order_time", "cadre", "lastfrom", "agent_id", "app_id", "user_id").
			Where("pay_time > ? and pay_time < ?", startTime, endTime).
			Where("agent_id = ?", agentID).
			Where("deleted = 0").Order("id asc").
			Limit(pageSize).Offset(page * pageSize).Find(&orderList).Error
		if err != nil {
			zlog.Warnf(ctx, "[RerunAgentOrder] Get subOrderList failed: err=[%v]", err)
			return err
		}
		if len(orderList) <= 0 {
			break
		}
		orderNumber += len(orderList)
		for _, orderInfo := range orderList {
			// 更新每个订单的归属人
			err = s.refreshOrderCadre(ctx, methodType, orderInfo, defaultName)
			if err != nil {
				zlog.Warnf(ctx, "[RerunAgentOrder] refreshOrderCadre failed: orderInfo.ID=[%v], err=[%v]", orderInfo.ID, err)
				continue
			}
		}
	}
	zlog.Infof(ctx, "[RerunAgentOrder] Get orderList agentID=[%v] number=[%v]", agentID, orderNumber)
	return
}

// 刷新订单归属人
func (s service) refreshOrderCadre(ctx *gin.Context, methodType string, orderInfo AFXUnionOrderInfo, defaultName string) (err error) {
	//2. 根据order请求courseorder表中subject_id和grade_id
	var courseOrderInfo tfmodels.QudaoCourseOrder
	err = helpers.MysqlClientQD.WithContext(ctx).Model(&tfmodels.QudaoCourseOrder{}).
		Where("trade_id = ?", orderInfo.OrderID).
		Where("user_id = ?", orderInfo.UserID).
		Find(&courseOrderInfo).Error
	if err != nil {
		zlog.Warnf(ctx, "[refreshOrderCadre] Get courseOrderInfo failed:  orderInfo.OrderID=[%v], err=[%v]", orderInfo.OrderID, err)
		return err
	}
	if courseOrderInfo.ID <= 0 {
		zlog.Infof(ctx, "[refreshOrderCadre] Get courseOrderInfo empty:  orderInfo.OrderID=[%v]", orderInfo.OrderID)
		return nil
	}
	//3. 按照匹配规则获取归属人
	newCadre := GetOrderCadre(ctx, orderInfo.Lastfrom, orderInfo.AgentID,
		orderInfo.AppID, courseOrderInfo.MainGradeID,
		courseOrderInfo.MainSubjectID, orderInfo.OrderTime)
	if len(newCadre) == 0 {
		newCadre = defaultName
		if len(defaultName) == 0 {
			zlog.Warnf(ctx, "[refreshOrderCadre] Get defaultName failed:  orderInfo.OrderID=[%v], err=[%v]", orderInfo.OrderID, err)
			return nil
		}
	}
	//4. 如果表中归属人和新得到的归属人不一致
	if len(newCadre) > 0 && newCadre != orderInfo.Cadre {
		zlog.Infof(ctx, "[refreshOrderCadre] need update Cadre orderInfo.ID=[%v], oldCadre=[%v], newCadre=[%v]", orderInfo.ID, orderInfo.Cadre, newCadre)
		if methodType == "update" {
			err = helpers.MysqlClient.WithContext(ctx).Table("tblAFXUnionOrder").
				Where("id = ?", orderInfo.ID).Update("cadre", newCadre).Error
			if err != nil {
				zlog.Warnf(ctx, "[refreshOrderCadre] update cadre failed：orderInfo.ID=[%v], err=[%v]", orderInfo.ID, err)
			}
			if err == nil {
				zlog.Infof(ctx, "[refreshOrderCadre] update Cadre success orderInfo.ID=[%v], oldCadre=[%v], newCadre=[%v]", orderInfo.ID, orderInfo.Cadre, newCadre)
			}
		}
	}
	return nil
}

// 根据订单信息获取归属人
func GetOrderCadre(ctx *gin.Context, lastfrom string, instID int64, appID, gradeID, subjectID, orderTime int) (cadre string) {
	lastfromSlice := strings.Split(lastfrom, "_")
	if len(lastfromSlice) == 0 {
		return ""
	}
	var relationType, relationValue int
	if utils.InArrayStr(lastfromSlice[0], []string{"yk", "gzsc"}) {
		// 按学部归因
		relationType = 1
		for xuebu, grades := range data.GradeMap {
			if _, ok := grades[gradeID]; ok {
				relationValue = xuebu
			}
		}
	}
	if utils.InArrayStr(lastfromSlice[0], []string{"dyxz", "gx"}) {
		//按学科归因
		relationType = 2
		relationValue = subjectID
	}
	var userRelationInfo tfmodels.TblAFXUserRelation
	err := helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
		Where("inst_id = ?", instID).
		Where("app_id = ?", appID).
		Where("start_time < ? and (end_time > ? or end_time = 0)", orderTime, orderTime).
		Where("relation_type = ?", relationType).
		Where("relation_value = ?", relationValue).
		First(&userRelationInfo).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			zlog.Warnf(ctx, "get userRelationInfo failed: err =[%v]", err)
		}
		return ""
	}
	return userRelationInfo.Cadre
}

func (s service) FixAfxUserCadre(ctx *gin.Context, methodType string) (err error) {
	var userList []tfmodels.TblAFXUser

	err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUser{}).Where("id in ?", FixUserIDList).Find(&userList).Error
	if err != nil {
		zlog.Warnf(ctx, "[FixAfxUserCadre] GetUserList failed: err = [%v]", err)
		return err
	}

	for _, userInfo := range userList {
		var relationList []tfmodels.TblAFXUserRelation
		err = helpers.MysqlClient.WithContext(ctx).
			Model(&tfmodels.TblAFXUserRelation{}).
			Where("inst_id = ?", userInfo.InstID).
			Where("app_id = ?", userInfo.AppID).Find(&relationList).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAfxUserCadre] GetRelationList failed: err = [%v]", err)
			continue
		}
		if len(relationList) == 0 {
			continue
		}
		needFix := true
		newCadre := ""
		for i := range relationList {
			if len(newCadre) == 0 {
				newCadre = userInfo.Cadre
			}
			if relationList[i].Cadre == userInfo.Cadre {
				needFix = false
				continue
			}
		}
		if needFix && len(newCadre) > 0 {
			zlog.Infof(ctx, "[FixAfxUserCadre] need update cadre: userInfo.ID = [%v], oldCadre = [%v], newCadre = [%v]", userInfo.ID, userInfo.Cadre, newCadre)
			if methodType == "update" {
				err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUser{}).
					Where("id = ?", userInfo.ID).
					Update("cadre", newCadre).Error
				if err != nil {
					zlog.Warnf(ctx, "[FixAfxUserCadre] update cadre failed: err = [%v]", err)
					continue
				}
				if err == nil {
					zlog.Infof(ctx, "[FixAfxUserCadre] update cadre success: userInfo.ID = [%v], oldCadre = [%v], newCadre = [%v]", userInfo.ID, userInfo.Cadre, newCadre)
				}
			}
		}
	}
	return nil
}

func (s service) FixAFXUserRelationData(ctx *gin.Context, methodType string) (err error) {
	switch methodType {
	case "FixAFXUserRelationDataInvalid":
		err = s.FixAFXUserRelationDataInvalid(ctx)
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] FixAFXUserRelationDataInvalid failed: err = [%v]", err)
			return err
		}
	case "FixAFXUserRelationDataFxInstID0":
		err = s.FixAFXUserRelationDataFxInstID0(ctx)
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] FixAFXUserRelationDataFxInstID0 failed: err = [%v]", err)
			return err
		}
	case "FixAFXUserRelationDataAppIDError":
		err = s.FixAFXUserRelationDataAppIDError(ctx)
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] FixAFXUserRelationDataAppIDError failed: err = [%v]", err)
			return err
		}
	case "FixAFXUserRelationDataStatus":
		err = s.FixAFXUserRelationDataStatus(ctx)
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] FixAFXUserRelationDataStatus failed: err = [%v]", err)
			return err
		}
	case "FixAFXUserRelationDataStatusRelease":
		err = s.FixAFXUserRelationDataStatusRelease(ctx)
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] FixAFXUserRelationDataStatusRelease failed: err = [%v]", err)
			return err
		}
	}
	return nil
}

// relationValue多刷的脏数据
func (s service) FixAFXUserRelationDataInvalid(ctx *gin.Context) (err error) {
	// 按照下面sql删除下面数据

	// delete from tblAFXUserRelation where promote_lv1=449 and relation_value != 1 limit 72;
	res := helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 449, 1).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 499, 1 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=449 and relation_value != 1 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=450 and relation_value != 20 limit 1434;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 450, 20).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 450, 2 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=450 and relation_value != 2 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=451 and relation_value != 30 limit 4368;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 451, 30).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 451, 30 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=451 and relation_value != 30 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=7 and relation_value != 1 limit 1950;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 7, 1).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 7, 1 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=1 and relation_value != 1 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=8 and relation_value != 20 limit 972;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 8, 20).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 8, 20 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=8 and relation_value != 20 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=9 and relation_value != 30 limit 975;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value != ?", 9, 30).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 9, 30 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=9 and relation_value != 30 affect rows: %d", res.RowsAffected)
	time.Sleep(1 * time.Second)

	//delete from tblAFXUserRelation where promote_lv1=452 and relation_value != 40 and relation_value != 41 limit 2;
	res = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("promote_lv1 = ? and relation_value not in ?", 452, []int{40, 41}).Delete(&models.ZtfDataDetail{})
	if res.Error != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataInvalid] delete 452, 40 41 failed: err=[%v]", err)
		return err
	}
	zlog.Infof(ctx, "[FixAFXUserRelationDataInvalid] promote_lv1=452 and relation_value not in (40， 41) affect rows: %d", res.RowsAffected)
	return nil
}

// 删除fxinstID=0在relation表中的脏数据
func (s service) FixAFXUserRelationDataFxInstID0(ctx *gin.Context) (err error) {
	/*
		delete
		from tblAFXUserRelation
		where id in
		      (select a.id
		       from tblAFXUserRelation a
		                left join (
		           SELECT *
		           FROM tblAFXInst
		           WHERE fx_inst_id = 0
		       ) b on a.inst_id = b.id
		       where b.id is not null)
		limit 373;
	*/
	// 1. 查找tblAFXInst中fx_inst_id = 0的数据 instIDs
	instIDs := make([]int64, 0)
	err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXInst{}).Where("fx_inst_id = ?", 0).Pluck("id", &instIDs).Error
	if len(instIDs) == 0 {
		return nil
	}
	zlog.Infof(ctx, "[FixAFXUserRelationData] instIDs=[%v]", instIDs)
	// 2. 根据instIDs，查找tblAFXUserRelation中inst_id = instIDs的数据 ids
	var relationList []tfmodels.TblAFXUserRelation
	err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("inst_id in ?", instIDs).Find(&relationList).Error
	if err != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataFxInstID0] select relationList failed: err = [%v]", err)
		return err
	}
	// 3. 删除tblAFXUserRelation中id in ids的数据
	if len(relationList) == 0 {
		return nil
	}
	for i := range relationList {
		err = helpers.MysqlClient.WithContext(ctx).Where("id = ?", relationList[i].ID).Delete(&tfmodels.TblAFXUserRelation{}).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationData] delete tblAFXUserRelation failed: err = [%v]", err)
			return err
		}
		zlog.Infof(ctx, "[FixAFXUserRelationData] delete relationList.ID = [%v]", relationList[i].ID)
		// 拼装回溯数据sql，防止回溯，打印insert relationList[i]语句到infof日志中
		insertSQL := fmt.Sprintf("INSERT INTO `tblAFXUserRelation` (`inst_id`, `app_id`, `promote_lv1`, `relation_type`, `relation_value`, `cadre`, `status`, `start_time`, `end_time`, `create_time`, `update_time`) VALUES (%d, %d, %d, %d, %d, '%s', %d, %d, %d, %d, %d);", relationList[i].InstID, relationList[i].AppID, relationList[i].PromoteLv1, relationList[i].RelationType, relationList[i].RelationValue, relationList[i].Cadre, relationList[i].Status, relationList[i].StartTime, relationList[i].EndTime, relationList[i].CreateTime, relationList[i].UpdateTime)
		zlog.Infof(ctx, "[FixAFXUserRelationData] rollback insertSQL = [%v]", insertSQL)
	}
	return nil
}

// relation表instID 对应的appid不正确的数据
func (s service) FixAFXUserRelationDataAppIDError(ctx *gin.Context) (err error) {
	/*
		delete
		from tblAFXUserRelation
		where id in
		      (select a.id
		       from tblAFXUserRelation a
		                left join tblAFXInst b on a.inst_id = b.id
		       where b.id is not null
		         and a.app_id != b.app_id)
		limit 1290;
	*/
	// 1. 查找tblAFXUserRelation中inst_id与tblAFXInst中id相等但是app_id不相等的数据
	var relationList []tfmodels.TblAFXUserRelation
	err = helpers.MysqlClient.WithContext(ctx).
		Raw(`select * from tblAFXUserRelation where id in (select a.id from tblAFXUserRelation a left join tblAFXInst b on a.inst_id = b.id  where b.id is not null  and a.app_id != b.app_id);`).
		Scan(&relationList).Error
	if err != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataAppIDError] select relationList failed: err = [%v]", err)
		return err
	}
	// 2. 删除这些数据
	for i := range relationList {
		err = helpers.MysqlClient.WithContext(ctx).Where("id = ?", relationList[i].ID).Delete(&tfmodels.TblAFXUserRelation{}).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationDataAppIDError] delete error: err = [%v]", err)
			return err
		}
		// 打印回溯sql
		insertSQL := fmt.Sprintf("INSERT INTO `tblAFXUserRelation` (`inst_id`, `app_id`, `promote_lv1`, `relation_type`, `relation_value`, `cadre`, `status`, `start_time`, `end_time`, `create_time`, `update_time`) VALUES (%d, %d, %d, %d, %d, '%s', %d, %d, %d, %d, %d);", relationList[i].InstID, relationList[i].AppID, relationList[i].PromoteLv1, relationList[i].RelationType, relationList[i].RelationValue, relationList[i].Cadre, relationList[i].Status, relationList[i].StartTime, relationList[i].EndTime, relationList[i].CreateTime, relationList[i].UpdateTime)
		zlog.Infof(ctx, "[FixAFXUserRelationDataAppIDError] rollback insertSQL = [%v]", insertSQL)
	}
	return nil
}

// 按照新释放公海逻辑，修改数据
func (s service) FixAFXUserRelationDataStatus(ctx *gin.Context) (err error) {
	// 表中status = 3 并且 end_time > 0的数据，status改为2
	// update tblAFXUserRelation set status = 2 where status = 3 and end_time > 0;
	var relationList []tfmodels.TblAFXUserRelation
	err = helpers.MysqlClient.WithContext(ctx).Where("status = ? and end_time > ?", 3, 0).Find(&relationList).Error
	if err != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataStatus] select relationList failed: err = [%v]", err)
		return err
	}
	for i := range relationList {
		err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).Where("id = ?", relationList[i].ID).Update("status", 2).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationDataStatus] update status failed: err = [%v]", err)
			return err
		}
		// 打印回溯sql
		rollbackSql := fmt.Sprintf("update tblAFXUserRelation set status = 3 where id = %d limit 1", relationList[i].ID)
		zlog.Infof(ctx, "[FixAFXUserRelationDataStatus] rollback insertSQL = [%v]", rollbackSql)
	}
	return nil
}

func (s service) FixAFXUserRelationDataStatusRelease(ctx *gin.Context) (err error) {
	// 找到表中status = 3 并且 end_time = 0的数据 relationList
	var relationList []tfmodels.TblAFXUserRelation
	err = helpers.MysqlClient.WithContext(ctx).Where("status = ? and end_time = ?", 3, 0).Find(&relationList).Error
	if err != nil {
		zlog.Warnf(ctx, "[FixAFXUserRelationDataStatusRelease] select relationList failed: err = [%v]", err)
		return err
	}
	// 遍历relationList判断表中是否存在与其inst_id,app_id,promote_lv1,relation_type,relation_value相同的数据。
	for i := range relationList {
		// 如果存在并且存在status=1的数据，将end_time置为将第一个大于当前start_time的数据的start_time赋值给end_time,status置为2
		var relationList2 []tfmodels.TblAFXUserRelation
		err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
			Where("inst_id = ?", relationList[i].InstID).
			Where("app_id = ?", relationList[i].AppID).
			Where("promote_lv1 = ?", relationList[i].PromoteLv1).
			Where("relation_type = ?", relationList[i].RelationType).
			Where("start_time > ?", relationList[i].StartTime).
			Where("relation_value = ?", relationList[i].RelationValue).
			Where("id != ?", relationList[i].ID).
			Order("start_time asc").
			Limit(1).
			Find(&relationList2).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationDataStatusRelease] select relationList2 failed: err = [%v]", err)
			return err
		}
		if len(relationList2) == 0 {
			continue
		}
		err = helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXUserRelation{}).
			Where("id = ?", relationList[i].ID).Updates(map[string]interface{}{
			"end_time": relationList2[0].StartTime,
			"status":   2,
		}).Error
		if err != nil {
			zlog.Warnf(ctx, "[FixAFXUserRelationDataStatusRelease] update end_time failed: err = [%v]", err)
			return err
		}
		// 打印回溯sql
		rollbackSql := fmt.Sprintf("update tblAFXUserRelation set end_time = 0, status = %d where id = %d limit 1", relationList[i].Status, relationList[i].ID)
		zlog.Infof(ctx, "[FixAFXUserRelationDataStatusRelease] rollback insertSQL = [%v]", rollbackSql)
	}
	return nil
}

func (s service) Options(ctx *gin.Context) (data.OptionsResp, error) {
	resp := data.OptionsResp{}
	results := make([]tfmodels.QudaoKV, 0)
	err := helpers.MysqlClientQD.WithContext(ctx).Table("tblQudaoKV").
		Where("type in ? and deleted = 0", []int{constant.KV_TYPE_PROMOTE_LV1, constant.KV_TYPE_PROMOTE_BUSINESS, constant.KV_TYPE_PROMOTE_GRADE_DEPTS, constant.KV_TYPE_PROMOTE_LV1_NEW}).Order("`order`").Find(&results).Error
	if err != nil {
		return resp, err
	}

	for _, result := range results {
		if result.Type == constant.KV_TYPE_PROMOTE_LV1 {
			if result.Key == 449 || result.Key == 450 || result.Key == 451 || result.Key == 7 || result.Key == 8 || result.Key == 9 {
				continue
			}
			resp.PromoteLv1 = append(resp.PromoteLv1, data.OptionItem{
				Key:   fmt.Sprintf("%d", result.Key),
				Value: result.Value,
			})
		} else if result.Type == constant.KV_TYPE_PROMOTE_BUSINESS {
			resp.PromoteBusiness = append(resp.PromoteBusiness, data.OptionItem{
				Key:   fmt.Sprintf("%d", result.Key),
				Value: result.Value,
			})
		} else if result.Type == constant.KV_TYPE_PROMOTE_GRADE_DEPTS {
			resp.PromoteGradeDept = append(resp.PromoteGradeDept, data.OptionItem{
				Key:   fmt.Sprintf("%d", result.Key),
				Value: result.Value,
			})
		} else if result.Type == constant.KV_TYPE_PROMOTE_LV1_NEW {
			resp.PromoteLv1New = append(resp.PromoteLv1New, data.OptionItem{
				Key:   fmt.Sprintf("%d", result.Key),
				Value: result.Value,
			})
		}
	}
	resp.PromoteLv1 = append([]data.OptionItem{{Key: "449,450", Value: "商务课程"},
		{Key: "451", Value: "商务课程-高中"},
		{Key: "7,8", Value: "0转正"},
		{Key: "9", Value: "0转正-高中"}}, resp.PromoteLv1...)

	resp.Tag = []data.OptionItem{
		{Key: 1, Value: "电商"},
		{Key: 2, Value: "地推"},
		{Key: 3, Value: "进校"},
		{Key: 4, Value: "短信"},
		{Key: 5, Value: "TMK"},
		{Key: 6, Value: "社群"},
		{Key: 7, Value: "三方媒体平台"},
		{Key: 8, Value: "公众号"},
		{Key: 9, Value: "其他"},
		{Key: 11, Value: "问卷"},
		{Key: 12, Value: "广告"},
		{Key: 13, Value: "分销"},
		{Key: 10, Value: "直播"},
		{Key: 14, Value: "公转私"},
		{Key: 15, Value: "T2P"},
		{Key: 16, Value: "线下机构"},
	}

	return resp, nil
}
