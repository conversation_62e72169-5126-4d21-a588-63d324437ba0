package afx

import (
	"fmt"
	"go-hetu/api/api_moat"
	"go-hetu/api/api_qdmis"
	"go-hetu/data"
	"go-hetu/helpers"
	"go-hetu/middleware"
	"go-hetu/models"
	"net/http"
	"sort"
	"strings"
	"time"

	"git.zuoyebang.cc/ad/gocommons/const/fenxiaoconsts"
	"git.zuoyebang.cc/ad/gocommons/tfmodels"
	"git.zuoyebang.cc/ad/gocommons/utils"
	libUtils "git.zuoyebang.cc/ad/gocommons/utils"
	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

const (
	ChannelLabelDs    = 1
	ChannelLabelDt    = 2
	ChannelLabelJx    = 3
	ChannelLabelDx    = 4
	ChannelLabelTmk   = 5
	ChannelLabelSq    = 6
	ChannelLabelMedia = 7
	ChannelLabelGzh   = 8
	ChannelLabelOther = 9
)

var ChannelLabelNames = map[int]string{
	ChannelLabelDs:    "电商",
	ChannelLabelDt:    "地推",
	ChannelLabelJx:    "进校",
	ChannelLabelDx:    "短信",
	ChannelLabelTmk:   "TMK",
	ChannelLabelSq:    "社群",
	ChannelLabelMedia: "三方媒体平台",
	ChannelLabelGzh:   "公众号",
	ChannelLabelOther: "其他",
}

func (s service) GetSelectList(ctx *gin.Context, params data.SelectListRequest) (res map[string]interface{}, err error) {
	fxDataAuth := middleware.GetFXDataAuthFromCtx(ctx)
	res = make(map[string]interface{})
	if len(params.DataType) == 0 {
		res["goodsType"], _ = goodsTypeList(ctx)
		res["app"], _ = appList(ctx, fxDataAuth)
		res["source"], _ = sourceList(ctx)
		res["goodsStatus"] = goodsStatusList()
		res["onsaleGoodsType"], err = onsaleGoodsTypeList(ctx)
		base.RenderJsonSucc(ctx, res)
		return
	}
	for _, dataType := range params.DataType {
		switch dataType {
		case "role": // 用户角色列表
			res[dataType], err = roleList()
		case "organType": // 机构类型列表
			res[dataType], err = organTypeList()
		case "organ": // 机构列表
			res[dataType], err = s.organList(ctx, &params, fxDataAuth)
		case "organContacts": // 机构联系人列表
			res[dataType], err = s.organContactsList(ctx, &params, fxDataAuth)
		case "goodsType": // 商品类型
			res[dataType], err = goodsTypeList(ctx)
		case "app": // 应用定义
			res[dataType], err = appList(ctx, fxDataAuth)
		case "source": // 业务线定义
			res[dataType], err = sourceList(ctx)
		case "goodsStatus": // 商品状态定义
			res[dataType] = goodsStatusList()
		case "onsaleGoodsType": //在售商品类型
			// todo
			res[dataType], err = onsaleGoodsTypeList(ctx)
		case "agentModelType": //代理商模式类型
			res[dataType] = agentModelTypeList()
		case "actType": // 活动类型
			res[dataType] = actTypeList()
		case "businessMark": // 业务标识:需要传接口方ID获取
			// TODO 没有找到对应的接口返回
			res[dataType], err = s.businessMark(ctx, &params)
		case "businessLine": // 业务线
			res[dataType], err = businessLineList(ctx)
		case "agentList": // 代理商列表
			res[dataType], err = s.getAgentList(ctx, &params, fxDataAuth)
		case "actStatus": // 活动状态
			res[dataType] = actStatusList()
		case "saleChannel": // 销售渠道
			res[dataType] = saleChannelList()
		case "brandType": // 数据看板业务类型
			res[dataType] = brandTypeList()
		case "tfProject": // 活动类型
			res[dataType], err = s.businessList(ctx, params.ProjectLv1Name)
		case "auth": // 权限列表
			res[dataType], err = s.getAuthList(ctx)
		case "orderSource": // 售卖类型
			res[dataType] = orderSourceList()
		case "orderStatus": // 订单状态
			res[dataType] = orderStatusList()
		}

		if err != nil {
			zlog.Error(ctx, errors.WithMessage(err, dataType))
			return
		}
	}
	return
}

func (s service) getAgentList(ctx *gin.Context, params *data.SelectListRequest, fxDataAuth *middleware.FXDataAuth) (list map[string]string, err error) {
	list = make(map[string]string)
	userCond := models.NewAfxUserCond()
	if len(fxDataAuth.AuthAppIds) > 0 {
		userCond.WithAppIds(fxDataAuth.AuthAppIds) // user middlewire.APPIDs
	}
	if len(fxDataAuth.AuthUser) > 0 {
		userCond.WithCadres(fxDataAuth.AuthUser) // user middlewire.InstIDs
	}
	userCond.WithCadre(params.Cadre)
	userCond.WithNickName(params.AgentName)
	userCond.WithRoleId(params.RoleId)
	userList, err := s.afxUserRepo.SelectByParams(ctx, userCond)
	if err != nil {
		return
	}
	instIdMap := make(map[int64]string)
	if params.AgentShow == 2 {
		instIds := make([]int64, 0)
		for _, userInfo := range userList {
			if _, ok := instIdMap[userInfo.InstID]; !ok {
				instIdMap[userInfo.InstID] = ""
				instIds = append(instIds, userInfo.InstID)
			}
		}

		instCond := models.NewAfxInstCond()
		instCond.WithInstIDs(instIds)
		instList, err := s.afxInstRepo.SelectByParams(ctx, instCond)
		if err != nil {
			return nil, err
		}
		for _, instInfo := range instList {
			if _, ok := instIdMap[instInfo.ID]; ok {
				instIdMap[instInfo.ID] = instInfo.Name
			}
		}
	}

	for _, userInfo := range userList {
		userId := cast.ToString(userInfo.ID)
		instName, ok := instIdMap[userInfo.InstID]
		if !ok {
			instName = ""
		}
		nickName := userInfo.Nickname
		if params.AgentShow == 2 {
			nickName = fmt.Sprintf("%d-%s-%s", userInfo.UID, nickName, instName)
		}
		// 使用tblAFXUser.id作为返回key
		if params.AgentKeyIsId > 0 {
			if _, ok := list[userId]; !ok {
				list[userId] = nickName
			}
		} else {
			// 使用 tblAFXUser.uid 作为返回的key
			if _, ok := list[userId]; !ok {
				list[userId] = nickName
			}

		}
	}
	return
}

func (s service) businessList(ctx *gin.Context, projectLv1Name string) (res *api_qdmis.GetProjectListResp, err error) {
	res = &api_qdmis.GetProjectListResp{}

	list := &api_qdmis.GetProjectListResp{}
	list, err = s.qdmisAPIService.GetProjectList(ctx, projectLv1Name)
	if err != nil {
		return nil, err
	}
	projectLv1 := middleware.GetCtxUserProjectLv1List(ctx)
	projectLv2 := middleware.GetCtxUserProjectLv2List(ctx)

	for _, v := range *list {
		if projectLv1 != nil && len(projectLv1) > 0 && !utils.InArrayInt64(v.Value, projectLv1) {
			continue
		}

		proItem := make([]api_qdmis.Child, 0)
		for _, child := range v.Children {
			if len(projectLv2) > 0 && !utils.InArrayInt64(child.Value, projectLv2) {
				continue
			}
			proItem = append(proItem, child)
		}
		v.Children = proItem
		*res = append(*res, v)
	}
	return
}

func (s service) getAuthList(ctx *gin.Context) (list []*AuthItem, err error) {
	cond := models.NewAfxAuthCond()
	cond.WithType(1)
	authList, err := s.afxAuthRepo.SelectByParams(ctx, cond)
	if err != nil {
		return
	}

	data := make(map[uint32]*AuthItem)
	for _, v := range authList {
		if _, ok := data[v.ID]; !ok {
			data[v.ID] = &AuthItem{
				ID:       v.ID,
				ParentID: v.Pid,
				Value:    v.ID,
				Label:    v.Name,
				Children: []*AuthItem{},
			}
		}
	}
	list = generateTree(data)

	sort.Slice(list, func(i, j int) bool {
		return list[i].ID < list[j].ID
	})

	return
}

type AuthItem struct {
	ID       uint32 `json:"id"`
	ParentID uint32 `json:"pid"`
	Value    uint32 `json:"value"`
	Label    string `json:"label"`
	Children []*AuthItem
}

func generateTree(items map[uint32]*AuthItem) []*AuthItem {
	tree := make([]*AuthItem, 0)

	for _, item := range items {
		if parent, ok := items[item.ParentID]; ok {
			parent.Children = append(parent.Children, item)
		} else {
			tree = append(tree, item)
		}
	}

	return tree
}

func roleList() (map[int]string, error) {
	return fenxiaoconsts.AFXAgentRoleMap, nil
}

func (s service) organList(ctx *gin.Context, params *data.SelectListRequest, fxDataAuth *middleware.FXDataAuth) (res map[int]string, err error) {
	list, err := s.getOrganInfoList(ctx, params, []string{"id", "name"}, fxDataAuth, 200)
	if err != nil {
		return nil, err
	}
	res = make(map[int]string, len(list))
	for _, v := range list {
		res[int(v.ID)] = v.Name
	}
	return res, nil
}

func (s service) organContactsList(ctx *gin.Context, params *data.SelectListRequest, fxDataAuth *middleware.FXDataAuth) (res map[int]string, err error) {
	list, err := s.getOrganInfoList(ctx, params, []string{"id", "contacts"}, fxDataAuth, 0)
	if err != nil {
		return nil, err
	}
	res = make(map[int]string, len(list))
	for _, v := range list {
		res[int(v.ID)] = v.Contacts
	}
	return res, nil
}

var fx2qdTagMap = map[int]int{
	1: 5,
	2: 1,
	3: 2,
	4: 3,
	5: 4,
}

func getQDTag(tag int) int {
	if tag > 0 && tag < 6 {
		return fx2qdTagMap[tag]
	}
	return tag
}

func (s service) getOrganInfoList(ctx *gin.Context, params *data.SelectListRequest, selectField []string, fxDataAuth *middleware.FXDataAuth, limit int) (res []*tfmodels.TblAFXInst, err error) {
	// 机构联系人是在一个应用方下的，必须输入appId才返回数据
	//if len(params.AppID) <= 0 {
	//	return nil, nil
	//}
	appIds := make([]string, 0)
	if params.AppID != "" { // 不为空的时候才解析
		appIds = strings.Split(params.AppID, ",")
	}
	if len(appIds) > 0 && params.PromoteBusiness <= 0 { // 伏羲机构报备-业务线为空时，根据appId映射获取
		params.PromoteBusiness = fenxiaoconsts.GetPromoteBusinessByAppID(cast.ToInt(appIds[0]))
		zlog.Infof(ctx, fmt.Sprintf("根据appId=%v映射获取 promoteBusiness=%d", appIds[0], params.PromoteBusiness))
	}

	fxInstIds := make([]int64, 0)

	/*
		if params.PromoteLv1 != "" || params.ChannelLabels != "" {
			regDb := helpers.MysqlClientQD.WithContext(ctx).Table(tfmodels.TblQudaoInstRegister).
				Where("run_status in (1,2)")
			if params.PromoteLv1 != "" {
				regDb = regDb.Where("promote_lv1 in (?)", strings.Split(params.PromoteLv1, ","))
			}
			if params.ChannelLabels != "" {
				tagList := strings.Split(params.ChannelLabels, ",")
				tagSql := ""
				for _, tag := range tagList {
					if tag == "" {
						continue
					}
					tagInt := cast.ToInt(tag)
					tagStr := fmt.Sprintf("{\"key\":%d}", getQDTag(tagInt))
					tagSql += fmt.Sprintf("JSON_CONTAINS(tag_info, '%s') or ", tagStr)
				}
				tagSql = "(" + strings.Trim(tagSql, " or ") + ")"
				regDb = regDb.Where(tagSql)
			}
			err = regDb.Distinct().Pluck("inst_id", &fxInstIds).Error
			if err != nil {
				return nil, err
			}
			if len(fxInstIds) == 0 {
				return res, nil
			}
		}
	*/
	if (params.PromoteBusiness > 0 && params.PromoteGradeDepts != "" && params.PromoteLv1New > 0) || params.ChannelLabels != "" {
		regDb := helpers.MysqlClientQD.WithContext(ctx).Table(tfmodels.TblQudaoInstRegister).
			Where("run_status in (1,2)")
		if params.PromoteBusiness > 0 {
			regDb = regDb.Where("promote_business = ? and promote_lv1_new = ? and promote_grade_dept in ? ",
				params.PromoteBusiness, params.PromoteLv1New, cast.ToIntSlice(strings.Split(params.PromoteGradeDepts, ",")))
		}
		if params.ChannelLabels != "" {
			tagList := strings.Split(params.ChannelLabels, ",")
			tagSql := ""
			for _, tag := range tagList {
				if tag == "" {
					continue
				}
				tagInt := cast.ToInt(tag)
				tagStr := fmt.Sprintf("{\"key\":%d}", getQDTag(tagInt))
				tagSql += fmt.Sprintf("JSON_CONTAINS(tag_info, '%s') or ", tagStr)
			}
			tagSql = "(" + strings.Trim(tagSql, " or ") + ")"
			regDb = regDb.Where(tagSql)
		}
		err = regDb.Distinct().Pluck("inst_id", &fxInstIds).Error
		if err != nil {
			return nil, err
		}
		if len(fxInstIds) == 0 {
			return res, nil
		}
	}

	db := helpers.MysqlClient.WithContext(ctx)

	if len(appIds) > 0 {
		db = db.Where("app_id in ?", appIds)
	}
	db = db.Where("status != ?", fenxiaoconsts.InstStatusInvalid)

	cadres := make([]string, 0)
	if params.Cadre != "" {
		cadres = append(cadres, params.Cadre)
	}
	authUsers := fxDataAuth.AuthUser
	authUsers = libUtils.RemoveRepeatedElement(authUsers)
	if len(authUsers) > 0 {
		cadres = append(cadres, authUsers...)
	}

	if len(fxInstIds) > 0 {
		db = db.Where("fx_inst_id in (?)", fxInstIds)
	}

	// 需求：归属人可见可操作
	instIDs, _ := s.getCadresFromUserRelationNew(ctx, cadres)
	if len(instIDs) == 0 {
		return res, nil
	}
	db = db.Where("id in (?)", instIDs)

	if params.OrganProvince != "" {
		db = db.Where("province = ?", params.OrganProvince)
	}
	if params.OrganCity != "" {
		db = db.Where("city = ?", params.OrganCity)
	}
	if params.OrganArea != "" {
		db = db.Where("area = ?", params.OrganArea)
	}
	if params.OrganName != "" {
		db = db.Where("name like ?", "%"+params.OrganName+"%")
	}
	if limit > 0 {
		db = db.Offset(0).Limit(limit)
	}
	db.Order("id desc")
	db = db.Select(selectField)
	err = db.Find(&res).Error
	if err != nil {
		return nil, err
	}
	return res, nil
}

func organTypeList() (map[int]string, error) {
	return fenxiaoconsts.InstTypeName, nil
}

func goodsTypeList(ctx *gin.Context) (interface{}, error) {
	res, err := Getallcategory(ctx)
	if err != nil {
		return nil, err
	}
	m := struct {
		GoodsCategoryRoot map[string]string
		GoodsCategoryTree map[string]map[string]string
		GoodsSourceTree   map[string]map[string]string
	}{
		GoodsCategoryRoot: res.GoodsCategoryRoot,
		GoodsCategoryTree: res.GoodsCategoryTree,
	}

	item := make(map[string]string)
	for catgoryPid := range m.GoodsCategoryTree {
		if v, ok := m.GoodsCategoryRoot[catgoryPid]; ok {
			item[catgoryPid] = v
		}
	}

	sourceIds := []string{"1", "2", "3", "101", "102"}
	m.GoodsSourceTree = make(map[string]map[string]string, len(sourceIds))
	for _, sourceId := range sourceIds {
		m.GoodsSourceTree[sourceId] = item
	}
	return m, nil
}

func appList(ctx *gin.Context, fxDataAuth *middleware.FXDataAuth) (res map[int]string, err error) {
	res = make(map[int]string)
	var list []*tfmodels.TblAFXApp
	db := helpers.MysqlClient.WithContext(ctx)
	db = db.Select([]string{"id", "name"})
	if len(fxDataAuth.AuthAppIds) > 0 {
		db = db.Where("id in ?", fxDataAuth.AuthAppIds)
	}

	err = db.Find(&list).Error
	if err != nil {
		return nil, err
	}
	for i := range list {
		res[int(list[i].ID)] = list[i].Name
	}

	return res, nil
}

func sourceList(ctx *gin.Context) (res map[int]string, err error) {
	var list []*tfmodels.TblAFXSkuCategory
	db := helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXSkuCategory{})
	db = db.Order("id asc")
	err = db.Select("distinct source_id as source_id").Find(&list).Error
	if err != nil {
		return nil, err
	}
	res = make(map[int]string, len(list))
	var sourceIDs []int

	for _, v := range list {
		sourceIDs = append(sourceIDs, v.SourceID)
	}
	for k, v := range fenxiaoconsts.SourceList {
		if utils.InArray(k, sourceIDs) {
			res[k] = v
		}
	}
	return map[int]string{}, nil
}

func goodsStatusList() map[int]string {
	return fenxiaoconsts.SkuStatusMap
}

func onsaleGoodsTypeList(ctx *gin.Context) (res map[string]interface{}, err error) {
	res = make(map[string]interface{})
	// 1. 拼装sql条件
	db := helpers.MysqlClient.WithContext(ctx).Model(&tfmodels.TblAFXSku{})
	// 开始时间小于等于当前时间start_time，并且结束时间end_time大于当前时间
	db.Where("start_time <= ?", time.Now().Unix()).Where("end_time > ?", time.Now().Unix())
	//"group by source_id,category_pid,category_id",
	//            "order by id asc"
	db.Order("id asc")
	db.Group("source_id,category_pid,category_id")
	db.Select("distinct source_id ,category_pid ,category_id")
	// 2. 从tblAFXSku表中获取skuList
	var skuList []*tfmodels.TblAFXSku
	err = db.Find(&skuList).Error
	if err != nil {
		zlog.Error(ctx, errors.WithMessage(err, "db.Find failed"))
		return nil, errors.WithMessage(err, "db.Find failed")
	}
	// 3. 调用接口Getallcategory获取result
	result, err := Getallcategory(ctx)
	if err != nil {
		zlog.Warn(ctx, errors.WithMessage(err, "Getallcategory failed"))
		return nil, errors.WithMessage(err, "Getallcategory failed")
	}
	// 4. 从result中获取goodsCategoryRoot、goodsCategoryTree
	goodsCategoryRoot := make(map[string]string)
	goodsCategoryTree := make(map[string]map[string]string)
	if result.GoodsCategoryRoot != nil {
		goodsCategoryRoot = result.GoodsCategoryRoot
	}
	if result.GoodsCategoryTree != nil {
		goodsCategoryTree = result.GoodsCategoryTree
	}

	res["goodsCategoryRoot"] = goodsCategoryRoot
	res["goodsCategoryTree"] = goodsCategoryTree
	if skuList == nil || len(skuList) == 0 {
		return res, nil
	}

	// 5. 遍历skuList，根据skuList中的categoryPid和sourceId
	//从$goodsCategoryRoot，/$goodsCategoryTree中获取goodsType分别放入$retGoodsCategoryRoot、$retGoodsCategoryTree
	retGoodsSourceTree := make(map[int]map[int]string)
	onsaleSource := make(map[int]string)

	for _, sku := range skuList {
		if _, ok := fenxiaoconsts.SourceList[sku.SourceID]; ok {
			if _, ok2 := retGoodsSourceTree[sku.SourceID]; !ok2 {
				retGoodsSourceTree[sku.SourceID] = make(map[int]string)
			}
			retGoodsSourceTree[sku.SourceID][sku.CategoryPID] = goodsCategoryRoot[cast.ToString(sku.CategoryPID)]
			onsaleSource[sku.SourceID] = fenxiaoconsts.SourceList[sku.SourceID]
		}
	}
	res["goodsSourceTree"] = retGoodsSourceTree
	res["onsaleSource"] = onsaleSource
	return
}

func brandTypeList() map[int]string {
	return map[int]string{
		1: "K12课程",
		4: "鸭鸭低幼",
	}
}

func (s service) businessMark(ctx *gin.Context, params *data.SelectListRequest) (list []interface{}, err error) {
	switch params.ApiSource {
	case fenxiaoconsts.AFXApiSourceDYZT:
		list, err = s.dyztAPIService.GetFenxiaoCategoryMap(ctx)
	}

	return
}

func businessLineList(ctx *gin.Context) (map[int]string, error) {
	authBusinessLine := middleware.GetCtxUserBusinessLine(ctx)
	res := map[int]string{}
	for k, v := range data.BusinessLineMap {
		if authBusinessLine != nil && len(authBusinessLine) > 0 && !utils.InArray(k, authBusinessLine) {
			continue
		}
		res[k] = v
	}
	return res, nil
}

func orderSourceList() map[int]string {
	return fenxiaoconsts.AFXOrderSourceNames
}

func orderStatusList() map[int]string {
	return fenxiaoconsts.AFXOrderStatusNames
}

func Getallcategory(ctx *gin.Context) (*AllCategory, error) {
	allCategory := new(AllCategory)
	opts := make(map[string]interface{})
	if err := api_moat.Moat(ctx, http.MethodGet, getallcategory, opts, allCategory); err != nil {
		return nil, errors.WithMessage(err, "moat")
	}
	return allCategory, nil
}

func agentModelTypeList() (res map[string]interface{}) {
	res = make(map[string]interface{})
	subTree := make(map[int]map[int]string)
	for k, values := range fenxiaoconsts.AFXAgentModelTypeTree {
		tmp := make(map[int]string)
		for _, v := range values {
			tmp[v] = fenxiaoconsts.AFXAgentCooperationTypeMap[v]
		}
		subTree[k] = tmp
	}
	res["subtree"] = subTree
	res["root"] = fenxiaoconsts.AFXAgentModelTypeMap
	return res
}

// 活动类型列表
func actTypeList() (res map[string]interface{}) {
	res = make(map[string]interface{})
	subTree := make(map[int]map[int]string)
	for k, values := range fenxiaoconsts.AFXActTypeTree {
		tmp := make(map[int]string)
		for _, v := range values {
			tmp[v] = fenxiaoconsts.AFXApiSourceMap[v]
		}
		subTree[k] = tmp
	}
	res["subtree"] = subTree
	res["root"] = fenxiaoconsts.AFXActTypeMap
	return res
}

func actStatusList() (res map[int]string) {
	return fenxiaoconsts.AFXActStatusMap
}

func saleChannelList() (res map[int]string) {
	return fenxiaoconsts.AFXSaleChannelMap
}
