package utils

import (
	"github.com/spf13/cast"
	"strconv"
	"strings"
)

func SplitToIntSlice(str, sep string) []int {
	return ToIntSlice(strings.Split(str, sep))
}

func ToIntSlice(str []string) []int {
	return cast.ToIntSlice(str)
}

func JoinSliceIntToStr(in []int64, seq string) string {
	var strSlice []string
	for _, i := range in {
		strSlice = append(strSlice, cast.ToString(i))
	}
	return strings.Join(strSlice, seq)
}

func SplitSlice(arr []int, num int) [][]int {
	max := len(arr)
	if max <= num {
		return [][]int{arr}
	}
	var quantity int
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	var segments = make([][]int, 0)
	var start, end, i int
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func UnicodeToZh(raw string) (string, error) {
	str, err := strconv.Unquote(strings.Replace(strconv.Quote(raw), `\\u`, `\u`, -1))
	if err != nil {
		return "", err
	}
	return str, nil
}

// SlicesEqualIgnoreOrder 判断两个 []int 切片元素是否相同（忽略顺序）
func SlicesEqualIgnoreOrder(slice1, slice2 []int) bool {
	if len(slice1) != len(slice2) {
		return false // 长度不同，直接返回 false
	}

	// 使用 map 统计每个切片的元素频率
	freq1 := make(map[int]int)
	freq2 := make(map[int]int)

	for _, num := range slice1 {
		freq1[num]++
	}

	for _, num := range slice2 {
		freq2[num]++
	}

	// 比较两个 map 是否相同
	for num, count := range freq1 {
		if freq2[num] != count {
			return false
		}
	}

	return true
}
